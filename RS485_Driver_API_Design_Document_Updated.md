# AI-SLDAP RS485 Driver API Design Document

## Executive Summary

This document presents a comprehensive API design for the AI-SLDAP RS485 Driver implementing the ZES proprietary protocol. Key features include:

- **Protocol-Compliant JSON Handling**: Native JSON parsing for 12-byte payload key-value pairs
- **Bidirectional Re-send Implementation**: Full support for both master and slave initiated re-send requests
- **Enhanced Broadcasting Safety**: Single-slave enforcement with multiple device detection
- **Proper Baud Rate Switching**: Complete S002 implementation with acknowledgment mechanism
- **Cross-Platform Data Consistency**: IEEE 754 and little-endian standardization

## Table of Contents

1. [Introduction](#1-introduction)
2. [Protocol Compliance Enhancements](#2-protocol-compliance-enhancements)
3. [API Overview](#3-api-overview)
4. [Protocol Implementation](#4-protocol-implementation)
5. [API Reference](#5-api-reference)
6. [Using the API](#6-using-the-api)
7. [Additional Features](#7-additional-features)
8. [Windows Driver Implementation Summary](#8-windows-driver-implementation-summary)
9. [Frequently Asked Questions (FAQ)](#9-frequently-asked-questions-faq)
10. [Conclusion](#10-conclusion)

## 1. Introduction

### 1.1 Purpose and Protocol Alignment

This document describes the API design for the AI-SLDAP RS485 Driver, implementing the ZES proprietary communication protocol for satellite On-Board Computer (OBC) applications. The design ensures full compliance with the RS485 Communication Software Protocol v1.1 specification.

**Key Protocol Compliance Features:**
- **JSON Payload Processing**: Native support for key-value pair data in 12-byte payload
- **Bidirectional Re-send**: Both master and slave can initiate re-send requests
- **Broadcast Acknowledgment**: Proper S002 baud rate switch with acknowledgment waiting
- **CRC8 Coverage**: Correct implementation covering 13 bytes (ID to payload, excluding header)
- **100ms Response Window**: Configurable timeout with collision handling

### 1.2 Scope and Platform Support

The API supports:
- **Windows** (10, 11) using Windows Driver Kit (WDK) with User-Mode Driver Framework (UMDF 2)
- **Linux** (Ubuntu 18.04+, CentOS 7+, RHEL 8+) using kernel module implementation
- **Cross-platform compatibility** with identical API interfaces and data formats
- **Real-time operation** suitable for airborne and industrial control systems
- **Multi-device support** (up to 30 slave devices with collision avoidance)

### 1.3 Key Design Features

- **Non-blocking Design**: Critical for airborne environments with multiple concurrent tasks
- **Protocol-Compliant Buffer Management**: 12-byte payload-centric design (5×12 uplink, 10×12 downlink)
- **Enhanced Error Recovery**: Bidirectional re-send with configurable retry policies
- **Cross-Platform Data Validation**: IEEE 754 and little-endian standardization
- **FIFO Guarantee**: Strict First-In-First-Out ordering for reliable data transmission

## 2. Protocol Compliance Enhancements

### 2.1 JSON Payload Processing

**Protocol Requirement**: The 12-byte payload contains key-value pair data

**Implementation Enhancement**:
```cpp
class PayloadDataExtractor {
public:
    // New: Native JSON parsing with validation
    JsonResult extractJson(const uint8_t payload[12], nlohmann::json& jsonData) {
        std::string jsonStr(reinterpret_cast<const char*>(payload),
                           strnlen(reinterpret_cast<const char*>(payload), 12));

        try {
            jsonData = nlohmann::json::parse(jsonStr);
            return JsonResult::SUCCESS;
        } catch (const nlohmann::json::parse_error& e) {
            // Fallback to binary interpretation for non-JSON responses
            return JsonResult::NOT_JSON_FORMAT;
        }
    }

    // Enhanced: Binary extraction with JSON awareness
    template<typename T>
    ExtractionResult extractValue(const uint8_t payload[12], T& value) {
        // First try JSON parsing
        nlohmann::json jsonData;
        if (extractJson(payload, jsonData) == JsonResult::SUCCESS) {
            return extractFromJson(jsonData, value);
        }

        // Fallback to binary extraction
        return extractBinary(payload + 4, value); // Skip 4-byte key
    }
};
```
### 2.2 Bidirectional Re-send Implementation

**Protocol Requirement**: Both master and slave can transmit re-send request frame (function code 0b000)

**Enhanced Implementation**:
```cpp
class ResendManager {
private:
    uint32_t m_maxRetries = 3;
    uint32_t m_retryDelayMs = 50;
    bool m_exponentialBackoff = false;

public:
    // Master-initiated re-send (existing)
    ConfigurationResult requestResend(uint8_t slaveAddress, uint8_t frameSequence) {
        ResendFrame frame;
        frame.functionCode = 0b000;
        frame.slaveAddress = slaveAddress;
        frame.sequenceNumber = frameSequence;

        return transmitFrame(frame);
    }

    // New: Slave-initiated re-send handling
    void handleSlaveResendRequest(const ResendFrame& request) {
        // Locate original frame in transmission history
        auto originalFrame = findFrameBySequence(request.sequenceNumber);
        if (originalFrame.has_value()) {
            // Retransmit original frame
            retransmitFrame(originalFrame.value());
        } else {
            // Frame not found in history - send error response
            sendErrorResponse(request.slaveAddress, ErrorCode::FRAME_NOT_FOUND);
        }
    }

    // Enhanced: Configurable retry policy
    void setRetryPolicy(uint32_t maxRetries, uint32_t delayMs, bool exponentialBackoff = false) {
        m_maxRetries = maxRetries;
        m_retryDelayMs = delayMs;
        m_exponentialBackoff = exponentialBackoff;
    }
};
```

### 2.3 Enhanced Broadcasting Safety

**Protocol Requirement**: Only use broadcast frame to assign slave address with single-slave limitation

**Improved Implementation**:
```cpp
class BroadcastManager {
private:
    bool m_strictSingleSlaveMode = true;

public:
    // Enhanced: Active device detection
    DetectionResult detectMultipleDevices() {
        std::vector<uint8_t> respondingAddresses;

        // Send ping to all possible addresses (1-31)
        for (uint8_t addr = 1; addr <= 31; addr++) {
            PingFrame ping;
            ping.targetAddress = addr;
            ping.timeout = 50; // Short timeout for detection

            if (sendPing(ping) == PingResult::SUCCESS) {
                respondingAddresses.push_back(addr);
            }
        }

        if (respondingAddresses.size() > 1) {
            logMultipleDevicesDetected(respondingAddresses);
            return DetectionResult::MULTIPLE_DEVICES_DETECTED;
        }

        return DetectionResult::SINGLE_DEVICE_CONFIRMED;
    }

    // Enhanced: Strict broadcasting with validation
    ConfigurationResult configureSystemSettings(const std::string& commandKey, uint64_t value) override {
        // Mandatory multiple device check for S-series commands
        if (m_strictSingleSlaveMode) {
            DetectionResult detection = detectMultipleDevices();
            if (detection == DetectionResult::MULTIPLE_DEVICES_DETECTED) {
                return ConfigurationResult::MULTIPLE_DEVICES_ERROR;
            }
        }

        // Proceed with broadcast
        return sendBroadcastCommand(commandKey, value);
    }
};
```

### 2.4 S002 Baud Rate Switch Implementation

**Protocol Requirement**: Slave starts at 9600, switches after broadcast S002, sends acknowledgment at new rate

**Complete Implementation**:
```cpp
class BaudRateManager {
private:
    uint32_t m_currentBaudRate = 9600;
    bool m_waitingForAck = false;

public:
    ConfigurationResult setBaudRate(uint32_t newBaudRate) {
        // Validate baud rate
        std::vector<uint32_t> validRates = {9600, 19200, 38400, 57600, 115200};
        if (std::find(validRates.begin(), validRates.end(), newBaudRate) == validRates.end()) {
            return ConfigurationResult::INVALID_PARAMETER;
        }

        // Send S002 broadcast at current rate
        ConfigurationResult result = sendBroadcastCommand("S002", newBaudRate);
        if (result != ConfigurationResult::SUCCESS) {
            return result;
        }

        // Switch master to new rate
        result = reconfigureHardwareBaudRate(newBaudRate);
        if (result != ConfigurationResult::SUCCESS) {
            // Revert to original rate
            reconfigureHardwareBaudRate(m_currentBaudRate);
            return ConfigurationResult::HARDWARE_ERROR;
        }

        // Wait for acknowledgment at new rate
        m_waitingForAck = true;
        AckResult ackResult = waitForBaudRateAck(newBaudRate, 500); // 500ms timeout
        m_waitingForAck = false;

        if (ackResult == AckResult::SUCCESS) {
            m_currentBaudRate = newBaudRate;
            return ConfigurationResult::SUCCESS;
        } else {
            // Revert to original rate on failure
            reconfigureHardwareBaudRate(m_currentBaudRate);
            return ConfigurationResult::TIMEOUT_ERROR;
        }
    }
};
```

### 2.5 CRC8 Implementation Correction

**Protocol Requirement**: CRC8 covers 13 bytes data from ID to data payload (excluding header byte)

**Corrected Implementation**:
```cpp
class CRC8Calculator {
private:
    static const uint8_t CRC8_POLYNOMIAL = 0x97; // Protocol-specified polynomial

public:
    uint8_t calculateCRC8(const uint8_t* data, size_t length) {
        uint8_t crc = 0x00;

        for (size_t i = 0; i < length; i++) {
            crc ^= data[i];
            for (int bit = 0; bit < 8; bit++) {
                if (crc & 0x80) {
                    crc = (crc << 1) ^ CRC8_POLYNOMIAL;
                } else {
                    crc <<= 1;
                }
            }
        }

        return crc;
    }

    // Protocol-compliant frame CRC calculation
    uint8_t calculateFrameCRC(const RS485Frame& frame) {
        // CRC covers 13 bytes: ID byte + 12-byte payload (excluding header 0xAA)
        return calculateCRC8(&frame.id_byte, 13);
    }

    bool validateFrame(const RS485Frame& frame) {
        uint8_t calculatedCRC = calculateFrameCRC(frame);
        return (calculatedCRC == frame.crc8);
    }
};
```

### 2.6 100ms Response Window Implementation

**Protocol Requirement**: 100ms response window with strict collision avoidance rules

**Enhanced Implementation**:
```cpp
class ResponseWindowManager {
private:
    static const uint32_t DEFAULT_RESPONSE_WINDOW_MS = 100;
    uint32_t m_responseWindowMs = DEFAULT_RESPONSE_WINDOW_MS;

public:
    // Configurable response window for different environments
    void setResponseWindow(uint32_t windowMs) {
        m_responseWindowMs = windowMs;
    }

    ResponseResult waitForResponse(uint8_t responseData[12]) {
        auto startTime = std::chrono::steady_clock::now();

        while (true) {
            auto currentTime = std::chrono::steady_clock::now();
            auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(
                currentTime - startTime).count();

            if (elapsedMs >= m_responseWindowMs) {
                return ResponseResult::TIMEOUT_ERROR;
            }

            // Check for available response
            if (checkResponseAvailable()) {
                return retrieveResponse(responseData);
            }

            // Small delay to prevent busy waiting
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
};
```




## 3. API Overview

### 3.1 Enhanced Error Management System

The API uses **specific result types** for different functional categories:

- **`ConnectionResult`**: For port opening/closing operations
- **`BufferResult`**: For buffer management operations
- **`ConfigurationResult`**: For device configuration operations
- **`RequestResult`/`ResponseResult`**: For data request/response operations
- **`HardwareResult`/`PerformanceResult`/`LineResult`**: For status monitoring operations
- **`EnumerationResult`/`DetectionResult`**: For device discovery operations

**Benefits:**
1. **Enhanced Code Readability**: Function signatures are self-documenting
2. **Intelligent Error Recovery**: Automatic categorization enables smart retry strategies
3. **Improved Type Safety**: Compiler catches incompatible result type comparisons
4. **Better Maintenance**: Organized error handling reduces cross-functional interference

### 3.2 Hardware Requirements

- **USB-RS485-WE-1800-BT FTDI converter** for PC-side interface
- **ZM-AISL-01 (FPGA) board** as slave device
- **Single twisted pair RS485 cable** in BUS topology
- **Maximum 32 devices** supported on the bus (30 slaves + 1 master)

### 3.3 Enhanced Driver Architecture

The implementation follows the OSI model adapted for ZM-AISL-01 protocol stack:
- **Physical Layer**: EIA/TIA-485 (RS485) via USB-RS485 converter with FTDI integration
- **Data Link Layer**: ZES proprietary protocol implemented as Windows User-Mode Driver Framework (UMDF 2) with buffer management and non-blocking operations
- **Application Layer**: User Application (via ZES driver API) with cross-platform compatibility and type-safe data handling

### 3.4 API Categories with Protocol Compliance

The API design follows the ZES driver API classification with five key components:

#### 3.4.1 ZES Driver API Classification and Function Code Correspondence

**Critical Design Principle: Function Code to API Category Mapping**

The five API categories directly correspond to the ZES protocol function codes in the ID byte:

| Function Code | Binary | Description | API Category |
|:-------------:|:------:|:------------|:-------------|
| **0b111** | 0b111 | Assign data | **Master Broadcasting API** + **Master Assign Data API** |
| **0b110** | 0b110 | Request data | **Master Request API** |
| **0b010** | 0b010 | Response to Assign | **Slave Response API** |
| **0b001** | 0b001 | Response to Request | **Slave Response API** |
| **0b000** | 0b000 | Re-send request | **Error Handle API** |

**Function Code Processing Logic:**
The driver automatically determines which API category to use based on the function code in the ID byte:
- **0b111 (Assign)**: Routes to Broadcasting API (S-series) or Assign Data API (U/W-series)
- **0b110 (Request)**: Routes to Master Request API (A-series)
- **0b010/0b001 (Responses)**: Routes to Slave Response API
- **0b000 (Re-send)**: Routes to Error Handle API for retry processing

The API is organized in the following logical order:

1. **Error Handle API**: `getErrorString(error)` + **Management APIs**
   - **Function Code**: 0b000 (Re-send request) - handles automatic retry mechanism
   - **FTDI-Style Management**: Port management functions similar to FTDI RS485 drivers
   - **Buffer Management**: Buffer status checking and overflow prevention
   - **Error Categorization**: Handles COM port errors with recovery strategies
   - **Management Functions**: `openPort()`, `closePort()`, `isPortOpen()`, `getBufferStatus()`, `clearBuffer()`
   - **Buffer Flag Checking**: Mandatory buffer status verification before transmission
   - **Retry Logic**: Smart error recovery with transient vs. permanent error categorization
   - **Thread Pool Management**: Dedicated thread pool for non-blocking operations

2. **Master Broadcasting API**: `configureSystemSettings(commandKey, value)` for S-series commands only
   - **Function Code**: 0b111 (Assign data) - for system-level broadcasting
   - **Buffer Check**: Automatically checks uplink buffer flag before transmission
   - **Hardware Requirement**: Only one slave device connected to prevent broadcast conflicts
   - **Address Assignment**: Sets the slave address used by subsequent U-series commands
   - **Runtime Validation**: Detects multiple slaves when only one is expected
   - **Acknowledgment**: Mandatory acknowledgment mechanism with timeout and retry logic (function code 0b010)

3. **Master Assign Data API**: `configureUserSettings(commandKey, value)` + `modelDataOperation(address, data, isWrite, length)`
   - **Function Code**: 0b111 (Assign data) - for targeted device configuration
   - **Buffer Check**: Automatically checks uplink buffer flag before transmission
   - **U-series Commands**: User configuration parameters using address from S001 command
   - **W-series Commands**: AI model weights and bias data stored in FRAM memory
   - **Address Resolution**: Uses slave address previously set by S001 or default address
   - **Acknowledgment**: Expects acknowledgment responses with timeout and retry logic (function code 0b010)
   - **Memory Safety**: Memory access validation for secure data operations

4. **Master Request API**: `requestData(dataKey)`
   - **Function Code**: 0b110 (Request data) - for information queries
   - **Buffer Check**: Automatically checks uplink buffer flag before transmission
   - **Non-blocking Design**: Returns immediately for asynchronous handling
   - **A-series Commands**: Application-related data queries and status requests
   - **Response Handling**: Expects data responses with background processing and automatic retry (function code 0b001)
   - **Airborne Compatibility**: Critical for multi-task airborne environments
   - **Performance Monitoring**: Real-time latency tracking and performance metrics

5. **Slave Response API**: `receiveSlaveResponse(responseData, waitForData, timeout)`
   - **Function Codes**: 0b010 (Response to Assign) and 0b001 (Response to Request)
   - **Buffer Management**: Automatically checks downlink buffer flag before storing data
   - **FIFO Processing**: Maintains strict First-In-First-Out ordering with per-slave queue management
   - **Response Types**: Handles both acknowledgments (0b010) and data responses (0b001)
   - **Buffer Overflow**: Configurable overflow policies when downlink buffer is full
   - **Data Ready Notification**: Callback mechanism for asynchronous data availability
   - **Multi-Frame Response Handling**: Automatically handles responses larger than 8 bytes
   - **Frame Sequencing**: For responses smaller than 8 bytes, data is right-aligned
   - **Cross-Platform Validation**: Ensures identical behavior across Windows and Linux platforms

This comprehensive API design allows users to focus on functional requirements without having to understand the underlying protocol details. The function code correspondence ensures that each API call is automatically routed to the correct protocol handling mechanism.







### 3.5 Management APIs and Buffer Control

**FTDI-Style Management Functions**

Following industry-standard serial port interface patterns, the RS485 driver includes comprehensive management APIs similar to FTDI RS485 drivers:

**Port Management APIs:**
```cpp
// Basic port operations (similar to FTDI FT_Open, FT_Close)
ConnectionResult openPort(const std::string& portName);
ConnectionResult closePort();
bool isPortOpen() const;
PortResult getPortInfo(PortInfo& info);

// Device enumeration (similar to FTDI FT_ListDevices)
static EnumerationResult enumerateDevices(std::vector<DeviceInfo>& deviceList);
static DetectionResult detectMultipleDevices(std::vector<uint8_t>& detectedAddresses);
```

**Buffer Management APIs (Critical for Data Integrity):**
```cpp
// Buffer status checking - MANDATORY before data transmission
BufferResult getBufferStatus(BufferStatus& status);
BufferResult checkUplinkBufferAvailability(bool& isFull);
BufferResult checkDownlinkBufferAvailability(bool& isFull);

// Buffer control operations
BufferResult clearBuffer(BufferType bufferType = BufferType::BOTH);
BufferResult setBufferOverflowPolicy(BufferOverflowPolicy policy);
BufferResult getBufferCapacity(uint32_t& uplinkFrames, uint32_t& downlinkFrames);

// Buffer monitoring
BufferResult setBufferThreshold(uint32_t thresholdPercent);
void registerBufferThresholdCallback(BufferThresholdCallbackFn callback);

// Per-slave buffer management
BufferResult getPerSlaveBufferStatus(uint8_t slaveAddress, BufferStatus& status);
```

**Hardware Status APIs (Similar to FTDI FT_GetStatus):**
```cpp
// Hardware and communication status
HardwareResult getHardwareStatus(HardwareStatus& status);
PerformanceResult getPerformanceMetrics(PerformanceMetrics& metrics);
ConfigResult getBaudRate(uint32_t& currentBaudRate);
LineResult getLineStatus(LineStatus& status);
```

**Critical Buffer Flag Checking:**

Every data transmission operation automatically performs buffer flag checking:

1. **Buffer Validation**: Driver checks uplink buffer flag to ensure space is available
2. **Storage Check**: Driver checks downlink buffer flag to prevent overflow
3. **FIFO Guarantee**: Strict First-In-First-Out ordering maintained
4. **Overflow Handling**: Configurable policies when buffers reach capacity

**Buffer Check Implementation:**
```cpp
// Internal implementation - automatically called before each transmission
BufferResult checkBufferBeforeTransmission() {
    BufferStatus status;
    BufferResult result = getBufferStatus(status);
    if (result != BufferResult::SUCCESS) {
        return result;
    }

    if (status.isUplinkFull) {
        // Apply overflow policy
        switch (m_bufferOverflowPolicy) {
            case BufferOverflowPolicy::DISCARD_OLDEST:
                clearOldestUplinkFrame();
                break;
            case BufferOverflowPolicy::DISCARD_NEWEST:
                return BufferResult::INSUFFICIENT_BUFFER;
            case BufferOverflowPolicy::TRIGGER_ERROR:
                return BufferResult::INSUFFICIENT_BUFFER;
        }
    }

    return BufferResult::SUCCESS;
}
```

This management API design follows industry-standard serial port communication patterns while providing the buffer management required for reliable RS485 communication.

### 3.6 Command Addressing Mechanism

**Important Note on A-series and U-series Command Addressing:**

The RS485 driver implements an internal addressing mechanism for A-series and U-series commands:

1. **A-series Commands (A001, A002, etc.)**: When using `requestData(dataKey)` for A-series commands, the driver automatically uses:
   - The slave address previously set by an S001 command via `configureSystemSettings`
   - The default slave address (0x00) if no S001 command has been executed

2. **U-series Commands (U001, U002, etc.)**: When using `configureUserSettings(commandKey, value)` for U-series commands, the driver automatically uses:
   - The slave address previously set by an S001 command via `configureSystemSettings`
   - The default slave address (0x00) if no S001 command has been executed

3. This design means that:
   - You should typically set the slave address using S001 before sending A-series or U-series commands
   - All A-series and U-series commands will be sent to the same slave address until a new S001 command is executed
   - If you need to communicate with multiple slaves, you must change the slave address using S001 between each set of commands

4. Sequence for proper addressing:
   - First use `configureSystemSettings("S001", slaveAddress)` to set the target slave address
   - **Driver handles addressing**: The driver automatically uses this address for all subsequent commands
   - Then use `configureUserSettings("Uxxxx", value)` for U-series commands to that slave
   - Then use `requestData("Axxx")` for A-series commands to that same slave
   - **Simplified API**: No need to specify slave address in `receiveSlaveResponse()` calls
   - Repeat this sequence for each slave device that needs configuration or data requests

**Recommended Usage Pattern:**
```cpp
// Step 1: Set the slave address (driver will use this internally)
uint8_t targetSlaveAddress = 5;
driver.configureSystemSettings("S001", targetSlaveAddress);

// Step 2: Driver automatically uses the configured address for all subsequent operations
driver.configureUserSettings("U001", 250);
driver.receiveSlaveResponse(responseData, 1000);

driver.requestData("A001");
driver.receiveSlaveResponse(responseData, 1000);

// Step 3: For multiple slaves, change address and repeat
targetSlaveAddress = 6;
driver.configureSystemSettings("S001", targetSlaveAddress);
// ... continue with new address
```

### 3.7 Command Categories and Definitions

The ZES protocol defines several categories of commands that can be exchanged between the master (PC) and slave (FPGA) devices:

#### 3.7.1 System Configuration Commands (S-series)

| Command | Description | Value Range | API Call Example |
|---------|-------------|-------------|------------------|
| **S001** | Set RS485 slave address | 1-31 | `configureSystemSettings('S001', 5)` |
| **S002** | Set baud rate | 9600, 19200, 38400, 57600, 115200 | `configureSystemSettings('S002', 115200)` |

**Important Notes on S-series Commands:**
- **S001 (Address Assignment)**: All AI-SLDAP boards are initialized with default address 0x00 during manufacturing. To assign a user-designated address, connect the board alone (without other slaves) and use broadcasting to address 0x00. The FPGA will write the new address to FRAM for persistence across power cycles.
  - **Conflict Prevention**: To prevent address conflicts, ensure only one slave device is connected to the RS485 bus during the S001 address assignment process. If multiple slaves respond to the S001 broadcast frame, the master should abort the assignment and alert the user to isolate a single slave.
- **S002 (Baud Rate Assignment)**: Slave devices use default baud rate 9600 upon power-on before assignment. After receiving S002 command, the slave switches to the new baud rate and sends an acknowledgment frame at the new baud rate. The master waits for this acknowledgment before continuing communication at the new baud rate.
  - **Confirmation Mechanism**: The slave acknowledges the baud rate change by sending a response frame (function code 0b010) at the newly assigned baud rate, which the master must receive before proceeding with further communication.

#### 3.7.2 User Configuration Commands (U-series)

| Command | Description | Value Range | API Call Example |
|---------|-------------|-------------|------------------|
| **U001** | Set SEL detection threshold | 40-500 milliampere | `configureUserSettings('U001', 250)` |
| **U002** | Set SEL maximum amplitude threshold | 1000-2000 milliampere | `configureUserSettings('U002', 1500)` |
| **U003** | Set number of SEL detections before power cycle | 1-5 | `configureUserSettings('U003', 3)` |
| **U004** | Set power cycle duration | 200, 400, 600, 800, or 1000 milliseconds | `configureUserSettings('U004', 600)` |
| **U005** | Enable/disable GPIO input functions | See GPIO Value Packing below | `configureUserSettings('U005', 0x0100)` |
| **U006** | Enable/disable GPIO output functions | See GPIO Value Packing below | `configureUserSettings('U006', 0x0101)` |

**Configuration Persistence:**
All U-series configuration settings are automatically saved to the FPGA board's FRAM (Ferroelectric Random Access Memory) upon successful execution. This ensures that:
- **Non-volatile Storage**: Configuration settings persist across power cycles and system reboots
- **One-time Setup**: Users only need to configure the device once; settings are retained permanently
- **Immediate Effect**: Changes take effect immediately and are simultaneously saved to FRAM
- **Reliability**: FRAM technology provides high endurance and data retention without requiring external power

**Reading Current Configuration:**
To retrieve the current configuration values stored in FRAM, use the A005 command (see Section 1.4.3 Application Data Commands).

**GPIO Value Packing for U005/U006:**
The `value` parameter for GPIO commands uses the following bit layout:
```cpp
// GPIO Value Packing Format:
// Lower 8 bits: Channel ID (0 or 1)
// Next 8 bits: Enable/Disable flag (0 = disable, 1 = enable)
// Upper 48 bits: Reserved (set to 0)

uint64_t value = (channel_id & 0xFF) | ((enable_flag & 0xFF) << 8);

// Examples:
// Enable GPIO input channel 0:  value = 0 | (1 << 8) = 0x0100
// Disable GPIO input channel 1: value = 1 | (0 << 8) = 0x0001
// Enable GPIO output channel 1:  value = 1 | (1 << 8) = 0x0101
```

#### 3.7.3 Application Data Commands (A-series)

| Command | Description | Response Data | API Call Example |
|---------|-------------|---------------|------------------|
| **A001** | Request SEL event log | JSON structure with event records | `requestData('A001')` |
| **A002** | Request device status | Status flags (16-bit) | `requestData('A002')` |
| **A003** | Request firmware version | Version string | `requestData('A003')` |
| **A004** | Request system statistics | JSON structure with statistics | `requestData('A004')` |
| **A005** | Request current configuration | JSON structure with all current settings | `requestData('A005')` |

**Example A001 Response Format:**
```json
{
  "events": [
    {
      "id": 1,
      "timestamp": "2024-12-15T14:30:22Z",
      "type": "SEL_DETECTED",
      "current": 320,
      "action": "POWER_CYCLE"
    },
    {
      "id": 2,
      "timestamp": "2024-12-15T18:45:10Z",
      "type": "SEL_DETECTED",
      "current": 450,
      "action": "POWER_CYCLE"
    }
  ],
  "total_count": 2
}
```

**Example A004 Response Format:**
```json
{
  "statistics": {
    "total_runtime_hours": 720,
    "power_cycles": {
      "total": 15,
      "last_24h": 2,
      "last_7d": 8,
      "last_30d": 15
    },
    "sel_events": {
      "total": 12,
      "last_24h": 1,
      "last_7d": 6,
      "last_30d": 12
    },
    "average_current": 180
  },
  "timestamp": "2024-12-16T09:30:00Z"
}
```

**Example A005 Response Format:**
```json
{
  "configuration": {
    "system_settings": {
      "slave_address": 5,
      "baud_rate": 115200
    },
    "user_settings": {
      "sel_detection_threshold": 250,
      "sel_max_amplitude_threshold": 1500,
      "sel_detection_count": 3,
      "power_cycle_duration": 600,
      "gpio_input_channels": {
        "channel_0": true,
        "channel_1": false
      },
      "gpio_output_channels": {
        "channel_0": false,
        "channel_1": true
      }
    },
    "last_updated": "2024-12-16T10:15:30Z",
    "fram_status": "healthy"
  }
}
```

#### 3.7.4 Weight and Bias Data Commands (W-series)

| Operation | Description | Parameters | API Call Example |
|-----------|-------------|------------|------------------|
| **W001** | Write model data to FRAM | Memory address, data bytes | `modelDataOperation(0x1000, data, true, 8)` |
| **W002** | Read model data from FRAM | Memory address, length | `modelDataOperation(0x1000, data, false, 8)` |

### 3.8 Implementation Approach

- **PC Side**: Single Windows executable application incorporating Windows User-Mode Driver Framework (UMDF 2) with integrated FTDI VCP driver functionality
- **FPGA Side**: VHDL/Verilog implementation of the slave device functionality (ZM-AISL-01 boards)
- **Data Link Layer Implementation**: Native Windows driver using WDK implementing ZES protocol with embedded FTDI VCP driver capabilities
- **Hardware Interface**: Built-in support for USB-RS485-WE-1800-BT FTDI converter without requiring separate driver installation
- **Buffer Management**: Driver-managed payload buffers (5 uplink × 12 bytes + 10 downlink × 12 bytes) with FIFO integrity
- **API Design Philosophy**: High abstraction level with type-safe data handling and cross-platform compatibility
- **Deployment Model**: Single executable deployment eliminating driver installation complexity

### 3.9 Data Structure Design Decisions

**Fixed-Size Arrays vs. Dynamic Vectors**

The RS485 driver API uses **fixed-size arrays** instead of `std::vector` for payload data handling. This design decision provides several critical advantages:

**Why Fixed-Size Arrays (uint8_t data[12]):**
1. **Memory Predictability**: No dynamic allocation overhead or fragmentation
2. **Real-Time Performance**: Deterministic memory access patterns for airborne systems
3. **Cross-Platform Compatibility**: Works identically across different C++ implementations
4. **Cache Efficiency**: Fixed-size data structures improve CPU cache utilization
5. **Stack Allocation**: Data can be allocated on stack, avoiding heap management
6. **DeviceIoControl Compatibility**: Kernel drivers work better with fixed-size buffers

**Why Not std::vector:**
1. **Dynamic Allocation**: Unpredictable memory allocation can cause real-time issues
2. **Exception Safety**: Vector operations can throw exceptions in low-memory conditions
3. **Memory Overhead**: Vector has additional metadata overhead (size, capacity, etc.)
4. **Kernel Interface**: DeviceIoControl works more efficiently with fixed-size buffers

**Protocol Justification:**
The ZES protocol defines a fixed 12-byte payload size, making fixed-size arrays the natural choice:
- **Frame Structure**: Header(1) + ID(1) + **Payload(12)** + CRC(1) + Trailer(1) = 16 bytes
- **Payload Content**: Key(4 bytes) + Value(8 bytes) = 12 bytes total
- **No Variable Length**: Protocol does not support variable-length payloads

**Example Comparison:**
```cpp
// Fixed-size array approach (RECOMMENDED)
uint8_t responseData[12];
ResponseResult result = driver.receiveSlaveResponse(responseData, 100);

// Vector approach (NOT USED - for comparison only)
std::vector<uint8_t> responseData;
ResponseResult result = driver.receiveSlaveResponse(responseData, 100);
```

### 3.10 Protocol Layer Separation: Complete Frame vs Payload Data

**Critical Design Concept: Two-Layer Data Processing**

This section addresses a common confusion about data handling in the RS485 driver: the difference between complete frame processing and payload data handling.

**Layer 1: Driver-Level Frame Processing (16 bytes)**
```cpp
struct RS485Frame {
    uint8_t header;        // 0xAA - Frame start marker
    uint8_t id_byte;       // Function code (3 bits) + Device address (5 bits)
    uint8_t payload[12];   // Key (4 bytes) + Value (8 bytes) - APPLICATION DATA
    uint8_t crc8;          // CRC8 checksum for error detection
    uint8_t trailer;       // 0x0D - Frame end marker
};  // Total: 16 bytes
```

**Driver Responsibilities (Hidden from User):**
1. **Frame Synchronization**: Detect Header (0xAA) and Trailer (0x0D)
2. **Error Detection**: Verify CRC8 checksum
3. **ID Byte Processing**: Extract and process function code and device address
4. **Function Code Routing**: Route frames to appropriate API categories
5. **Address Filtering**: Ensure responses match expected device addresses

**Layer 2: User-Level Payload Processing (12 bytes)**
```cpp
// User application interface - only handles meaningful data
uint8_t responseData[12];  // Key (4 bytes) + Value (8 bytes)
ResponseResult result = driver.receiveSlaveResponse(responseData, 100);

// responseData structure:
// Bytes 0-3: Key (4-byte ASCII string, null-terminated if < 4 chars)
// Bytes 4-11: Value (8-byte binary data in little-endian format)
```

**Detailed Data Storage Format:**

**Key Storage (Bytes 0-3) - ASCII String Format:**
```cpp
// Example key storage patterns:
// "A001" → [0x41, 0x30, 0x30, 0x31] (4 ASCII characters)
// "U1"   → [0x55, 0x31, 0x00, 0x00] (2 chars + 2 null bytes)
// "S002" → [0x53, 0x30, 0x30, 0x32] (4 ASCII characters)

// Key extraction helper:
std::string extractKey(const uint8_t* payload) {
    char keyBuffer[5] = {0};  // 4 chars + null terminator
    memcpy(keyBuffer, payload, 4);
    return std::string(keyBuffer);
}
```

**Value Storage (Bytes 4-11) - Binary Little-Endian Format:**
```cpp
// Integer value storage (32-bit, uses bytes 4-7, bytes 8-11 set to zero):
uint32_t intValue = 1500;  // SEL threshold = 1500 mA
payload[4] = (intValue >> 0) & 0xFF;   // LSB
payload[5] = (intValue >> 8) & 0xFF;
payload[6] = (intValue >> 16) & 0xFF;
payload[7] = (intValue >> 24) & 0xFF;  // MSB
payload[8] = payload[9] = payload[10] = payload[11] = 0x00;  // Upper 4 bytes zero

// Float value storage (IEEE 754 single-precision, uses bytes 4-7):
float floatValue = 3.14159f;
uint32_t floatBits = *reinterpret_cast<uint32_t*>(&floatValue);
payload[4] = (floatBits >> 0) & 0xFF;   // LSB
payload[5] = (floatBits >> 8) & 0xFF;
payload[6] = (floatBits >> 16) & 0xFF;
payload[7] = (floatBits >> 24) & 0xFF;  // MSB
payload[8] = payload[9] = payload[10] = payload[11] = 0x00;  // Upper 4 bytes zero

// Double value storage (IEEE 754 double-precision, uses all bytes 4-11):
double doubleValue = 3.141592653589793;
uint64_t doubleBits = *reinterpret_cast<uint64_t*>(&doubleValue);
payload[4] = (doubleBits >> 0) & 0xFF;   // LSB
payload[5] = (doubleBits >> 8) & 0xFF;
payload[6] = (doubleBits >> 16) & 0xFF;
payload[7] = (doubleBits >> 24) & 0xFF;
payload[8] = (doubleBits >> 32) & 0xFF;
payload[9] = (doubleBits >> 40) & 0xFF;
payload[10] = (doubleBits >> 48) & 0xFF;
payload[11] = (doubleBits >> 56) & 0xFF; // MSB

// Dual integer storage (two 32-bit integers):
uint32_t value1 = 0;     // GPIO channel
uint32_t value2 = 1;     // Enable flag
// First integer in bytes 4-7:
payload[4] = (value1 >> 0) & 0xFF;
payload[5] = (value1 >> 8) & 0xFF;
payload[6] = (value1 >> 16) & 0xFF;
payload[7] = (value1 >> 24) & 0xFF;
// Second integer in bytes 8-11:
payload[8] = (value2 >> 0) & 0xFF;
payload[9] = (value2 >> 8) & 0xFF;
payload[10] = (value2 >> 16) & 0xFF;
payload[11] = (value2 >> 24) & 0xFF;
```

**User Responsibilities (Simplified):**
1. **Key Interpretation**: Understand command identifiers ("A001", "U001", etc.)
2. **Value Processing**: Handle 8-byte data values (integers, floats, structured data)
3. **Application Logic**: Implement business logic based on received data

**Data Extraction Helper Functions:**
```cpp
// Helper class for consistent data extraction from 12-byte payload
class PayloadDataExtractor {
public:
    // Extract key from bytes 0-3
    static std::string extractKey(const uint8_t* payload) {
        char keyBuffer[5] = {0};  // 4 chars + null terminator
        memcpy(keyBuffer, payload, 4);
        return std::string(keyBuffer);
    }

    // Extract 32-bit integer from bytes 4-7 (little-endian)
    static uint32_t extractInteger(const uint8_t* payload) {
        return (static_cast<uint32_t>(payload[4]) << 0) |
               (static_cast<uint32_t>(payload[5]) << 8) |
               (static_cast<uint32_t>(payload[6]) << 16) |
               (static_cast<uint32_t>(payload[7]) << 24);
    }

    // Extract IEEE 754 single-precision float from bytes 4-7
    static float extractFloat(const uint8_t* payload) {
        uint32_t floatBits = extractInteger(payload);
        return *reinterpret_cast<float*>(&floatBits);
    }

    // Extract IEEE 754 double-precision float from bytes 4-11
    static double extractDouble(const uint8_t* payload) {
        uint64_t doubleBits =
            (static_cast<uint64_t>(payload[4]) << 0) |
            (static_cast<uint64_t>(payload[5]) << 8) |
            (static_cast<uint64_t>(payload[6]) << 16) |
            (static_cast<uint64_t>(payload[7]) << 24) |
            (static_cast<uint64_t>(payload[8]) << 32) |
            (static_cast<uint64_t>(payload[9]) << 40) |
            (static_cast<uint64_t>(payload[10]) << 48) |
            (static_cast<uint64_t>(payload[11]) << 56);
        return *reinterpret_cast<double*>(&doubleBits);
    }

    // Extract dual 32-bit integers from bytes 4-7 and 8-11
    static std::pair<uint32_t, uint32_t> extractDualIntegers(const uint8_t* payload) {
        uint32_t value1 = extractInteger(payload);  // Bytes 4-7
        uint32_t value2 =
            (static_cast<uint32_t>(payload[8]) << 0) |
            (static_cast<uint32_t>(payload[9]) << 8) |
            (static_cast<uint32_t>(payload[10]) << 16) |
            (static_cast<uint32_t>(payload[11]) << 24);  // Bytes 8-11
        return std::make_pair(value1, value2);
    }

    // Store key into bytes 0-3 (ASCII string, null-padded)
    static void storeKey(uint8_t* payload, const std::string& key) {
        memset(payload, 0, 4);  // Clear first 4 bytes
        size_t copyLen = std::min(key.length(), static_cast<size_t>(4));
        memcpy(payload, key.c_str(), copyLen);
    }

    // Store 32-bit integer into bytes 4-7 (little-endian, zero upper bytes)
    static void storeInteger(uint8_t* payload, uint32_t value) {
        payload[4] = (value >> 0) & 0xFF;
        payload[5] = (value >> 8) & 0xFF;
        payload[6] = (value >> 16) & 0xFF;
        payload[7] = (value >> 24) & 0xFF;
        payload[8] = payload[9] = payload[10] = payload[11] = 0x00;  // Clear upper bytes
    }

    // Store IEEE 754 float into bytes 4-7 (zero upper bytes)
    static void storeFloat(uint8_t* payload, float value) {
        uint32_t floatBits = *reinterpret_cast<uint32_t*>(&value);
        storeInteger(payload, floatBits);
    }

    // Store IEEE 754 double into bytes 4-11
    static void storeDouble(uint8_t* payload, double value) {
        uint64_t doubleBits = *reinterpret_cast<uint64_t*>(&value);
        payload[4] = (doubleBits >> 0) & 0xFF;
        payload[5] = (doubleBits >> 8) & 0xFF;
        payload[6] = (doubleBits >> 16) & 0xFF;
        payload[7] = (doubleBits >> 24) & 0xFF;
        payload[8] = (doubleBits >> 32) & 0xFF;
        payload[9] = (doubleBits >> 40) & 0xFF;
        payload[10] = (doubleBits >> 48) & 0xFF;
        payload[11] = (doubleBits >> 56) & 0xFF;
    }

    // Store dual 32-bit integers into bytes 4-7 and 8-11
    static void storeDualIntegers(uint8_t* payload, uint32_t value1, uint32_t value2) {
        // Store first integer in bytes 4-7
        payload[4] = (value1 >> 0) & 0xFF;
        payload[5] = (value1 >> 8) & 0xFF;
        payload[6] = (value1 >> 16) & 0xFF;
        payload[7] = (value1 >> 24) & 0xFF;
        // Store second integer in bytes 8-11
        payload[8] = (value2 >> 0) & 0xFF;
        payload[9] = (value2 >> 8) & 0xFF;
        payload[10] = (value2 >> 16) & 0xFF;
        payload[11] = (value2 >> 24) & 0xFF;
    }
};
```

**ID Byte Processing Example (Driver Internal):**
```cpp
// This processing happens automatically in the driver - users never see this code
void ProcessIncomingFrame(const RS485Frame& frame) {
    // Extract function code from ID byte (bits 7-5)
    uint8_t functionCode = (frame.id_byte >> 5) & 0x07;

    // Extract device address from ID byte (bits 4-0)
    uint8_t deviceAddress = frame.id_byte & 0x1F;

    // Automatic routing based on function code
    switch (functionCode) {
        case 0b111:  // Assign data - Routes to Broadcasting/Assign Data API
            ProcessAssignDataFrame(frame.payload, deviceAddress);
            break;

        case 0b110:  // Request data - Routes to Master Request API
            ProcessRequestDataFrame(frame.payload, deviceAddress);
            break;

        case 0b010:  // Response to Assign - Routes to Slave Response API
            ProcessResponseAssignFrame(frame.payload, deviceAddress);
            break;

        case 0b001:  // Response to Request - Routes to Slave Response API
            ProcessResponseRequestFrame(frame.payload, deviceAddress);
            break;

        case 0b000:  // Re-send request - Routes to Error Handle API
            ProcessResendRequestFrame(frame.payload, deviceAddress);
            break;
    }

    // Only the 12-byte payload is passed to user application
    // All protocol overhead (Header, ID, CRC, Trailer) is handled internally
}
```

**Key Benefits of This Separation:**
1. **User Simplicity**: Applications only handle meaningful 12-byte data
2. **Protocol Reliability**: Driver ensures all frames are valid before passing data to user
3. **Automatic Routing**: Function codes automatically route to correct API categories
4. **Error Isolation**: Protocol errors are handled at driver level, not user level
5. **Address Management**: Driver automatically filters responses by device address

**Practical Usage Examples:**
```cpp
// Example 1: Receiving A001 response (SEL event log)
// Note: Driver automatically uses the address configured via S001
uint8_t responseData[12];
ResponseResult result = driver.receiveSlaveResponse(responseData, 100);

if (result == ResponseResult::SUCCESS) {
    std::string key = PayloadDataExtractor::extractKey(responseData);
    if (key == "A001") {
        // For A001, the 8-byte value contains JSON data pointer or structured data
        uint64_t jsonDataPointer = PayloadDataExtractor::extractDouble(responseData);
        // Process JSON data...
    }
}

// Example 2: Receiving U001 configuration confirmation
uint8_t responseData[12];
ResponseResult result = driver.receiveSlaveResponse(responseData, 100);

if (result == ResponseResult::SUCCESS) {
    std::string key = PayloadDataExtractor::extractKey(responseData);
    if (key == "U001") {
        // U001 response contains the configured threshold value
        uint32_t threshold = PayloadDataExtractor::extractInteger(responseData);
        std::cout << "SEL threshold set to: " << threshold << " mA" << std::endl;
    }
}

// Example 3: Receiving U005 GPIO configuration confirmation
uint8_t responseData[12];
ResponseResult result = driver.receiveSlaveResponse(responseData, 100);

if (result == ResponseResult::SUCCESS) {
    std::string key = PayloadDataExtractor::extractKey(responseData);
    if (key == "U005") {
        // U005 response contains channel and enable flag
        auto [channel, enableFlag] = PayloadDataExtractor::extractDualIntegers(responseData);
        std::cout << "GPIO input channel " << channel
                  << (enableFlag ? " enabled" : " disabled") << std::endl;
    }
}

// Example 4: Sending configuration with proper payload formatting
uint8_t configPayload[12];
PayloadDataExtractor::storeKey(configPayload, "U001");
PayloadDataExtractor::storeInteger(configPayload, 250);  // 250 mA threshold

ConfigurationResult result = driver.configureUserSettings(configPayload);
```

**Data Type Validation and Safety:**
```cpp
// Type-safe wrapper functions for common operations
class RS485DataHandler {
public:
    // Safe integer configuration with range validation
    static ConfigurationResult configureIntegerSetting(
        RS485Driver& driver,
        const std::string& key,
        uint32_t value,
        uint32_t minValue,
        uint32_t maxValue) {

        if (value < minValue || value > maxValue) {
            return ConfigurationResult::INVALID_PARAMETER;
        }

        uint8_t payload[12];
        PayloadDataExtractor::storeKey(payload, key);
        PayloadDataExtractor::storeInteger(payload, value);

        return driver.configureUserSettings(payload);
    }

    // Safe float configuration with validation
    static ConfigurationResult configureFloatSetting(
        RS485Driver& driver,
        const std::string& key,
        float value,
        float minValue,
        float maxValue) {

        if (value < minValue || value > maxValue ||
            !std::isfinite(value) || std::isnan(value)) {
            return ConfigurationResult::INVALID_PARAMETER;
        }

        uint8_t payload[12];
        PayloadDataExtractor::storeKey(payload, key);
        PayloadDataExtractor::storeFloat(payload, value);

        return driver.configureUserSettings(payload);
    }

    // Safe dual integer configuration (for GPIO commands)
    static ConfigurationResult configureDualIntegerSetting(
        RS485Driver& driver,
        const std::string& key,
        uint32_t value1,
        uint32_t value2) {

        uint8_t payload[12];
        PayloadDataExtractor::storeKey(payload, key);
        PayloadDataExtractor::storeDualIntegers(payload, value1, value2);

        return driver.configureUserSettings(payload);
    }
};

// Usage examples with type safety:
// Configure SEL threshold (250 mA, valid range 40-500)
RS485DataHandler::configureIntegerSetting(driver, "U001", 250, 40, 500);

// Configure GPIO input channel 0 enable
RS485DataHandler::configureDualIntegerSetting(driver, "U005", 0, 1);  // Channel 0, Enable

// Configure power cycle duration (600 ms, valid values: 200,400,600,800,1000)
RS485DataHandler::configureIntegerSetting(driver, "U004", 600, 200, 1000);
```

**Common Misconception Clarification:**
- **Misconception**: "Users need to process the complete 16-byte frame"
- **Reality**: Users only process the 12-byte payload; driver handles all protocol details
- **Misconception**: "Users need to manually parse ID byte for function codes"
- **Reality**: Driver automatically routes frames based on function codes in ID byte
- **Misconception**: "String keys are stored as null-terminated C strings"
- **Reality**: Keys are stored as fixed 4-byte ASCII data, null-padded if shorter than 4 characters
- **Misconception**: "Different data types require different payload sizes"
- **Reality**: All payloads are exactly 12 bytes; data types are differentiated by interpretation of the 8-byte value portion

### 3.11 API Data Format Specification

**Critical Design Principle: Universal Cross-Platform Data Format**

The RS485 driver API uses a universal data format that ensures compatibility across different systems (Windows, Linux), programming languages (C++, Python, Java, C#), and architectures (x86, x64, ARM). Users only need to provide raw data values - the driver automatically handles all data type conversion and formatting internally.

**User-Friendly Data Input:**
- **No Type Specification Required**: Users input raw integer or floating-point values directly
- **Automatic Format Conversion**: Driver internally converts user data to the correct binary format
- **Type-Safe Processing**: Compile-time type safety prevents common programming errors
- **Transparent Processing**: Users don't need to understand binary encoding, endianness, or payload structure
- **Cross-Platform Consistency**: Same user input produces identical results across all platforms

**API Parameter Format (Internal - Handled Automatically):**
- **Command Key**: 4-byte ASCII string identifier stored in bytes 0-3 (e.g., "S001", "U001", "A001")
- **Payload Data**: 8-byte binary value stored in bytes 4-11 with little-endian byte ordering

**Type-Safe User Interface Examples:**
```cpp
// User simply provides the raw value
driver.configureUserSettings("U001", 250);        // Integer: 250 mA threshold
driver.configureUserSettings("U004", 600);        // Integer: 600 ms duration
driver.configureUserSettings("W001", 3.14159f);   // Float: 3.14159 weight value
driver.configureUserSettings("U005", 0x100000000ULL);  // Channel=0, Enable=1

// Driver processing includes:
// 1. Compile-time type safety
// 2. Runtime validation
// 3. Automatic type detection
// 4. Binary format conversion
// 5. Cross-platform endianness handling
```



**Payload Data Format Guidelines:**

1. **Integer Values**: Uses bytes 4-7 in little-endian format, bytes 8-11 set to zero
2. **Dual Integer Values**: First integer in bytes 4-7, second integer in bytes 8-11 (both little-endian)
3. **Floating-Point Values**: IEEE 754 format - single precision in bytes 4-7, double precision in bytes 4-11
4. **Fixed-Point Values**: 32-bit integer in bytes 4-7 with documented scale factor

**Cross-Platform Compatibility:**
- **Byte Order**: All multi-byte values use little-endian format
- **Alignment**: No special alignment requirements
- **Padding**: Unused bytes set to zero
- **Type Safety**: API functions handle conversion from native types to wire format
- **Language Independence**: Format works identically across programming languages
- **Architecture Independence**: Same format on all processor architectures

**Automatic Data Type Conversion Examples:**
```cpp
// Users provide raw values - driver handles conversion automatically
driver.configureUserSettings("U001", 250);        // SEL threshold: 250 mA
driver.configureUserSettings("W001", 3.14159f);   // Weight value: 3.14159
driver.configureUserSettings("U005", 0x100000000ULL);  // Channel=0, Enable=1
driver.configureSystemSettings("S001", 5);        // Slave address: 5

// Driver automatically handles data type conversion and formatting
```





**User-Friendly API Design Summary:**

The key principle of this RS485 driver API is **simplicity for end users**. Users interact with the driver using natural, intuitive function calls without needing to understand protocol details, data encoding, or cross-platform compatibility issues.

### 3.12 Windows Driver Kit (WDK) Integration and Application Architecture

**Application Framework Selection:**
The RS485 communication application integrates Windows Driver Kit (WDK) User-Mode Driver Framework (UMDF 2) within a single executable, providing simplified deployment, system stability, and built-in FTDI VCP driver functionality.

**Key WDK Components Integrated:**
- **UMDF 2.0**: User-Mode Driver Framework
- **WDF I/O Queues**: Managing communication requests and asynchronous operations
- **WDF Device Objects**: Representing the RS485 device interface
- **WDF Memory Objects**: Efficient buffer management and data transfer
- **FTDI VCP Integration**: Embedded FTDI Virtual COM Port driver functionality

**Application Deployment Model:**
- **Single Executable**: Complete solution packaged as RS485_Communication_Application.exe
- **No Driver Installation**: Application handles all driver functionality internally
- **Plug and Play Support**: Automatic detection and configuration of USB-RS485 converters
- **Windows Compatibility**: Full compatibility with Windows 10/11

## 2. Architecture Overview

### 2.0 Integrated Application Architecture

The RS485 communication application integrates Windows Driver Kit (WDK) User-Mode Driver Framework (UMDF 2) with embedded FTDI VCP driver functionality, providing a complete RS485 communication solution in a single executable.

**Integrated Application Architecture:**
```
┌─────────────────────────────────────┐
│   RS485_Communication_Application   │
│              (.exe)                 │
├─────────────────────────────────────┤
│        High-Level API Layer         │
│  • configureSystemSettings()        │
│  • configureUserSettings()          │
│  • requestData()                    │
│  • receiveSlaveResponse()           │
│  • modelDataOperation()             │
├─────────────────────────────────────┤
│   Integrated UMDF 2.0 Framework     │
│  ┌─────────────────────────────────┐│
│  │   Driver-Managed Payload Buffers││
│  │  ┌─────────────┬─────────────┐  ││
│  │  │ Uplink (5)  │Downlink(10) │  ││
│  │  │ 5×12 bytes  │ 10×12 bytes │  ││
│  │  │ = 60 bytes  │ = 120 bytes │  ││
│  │  └─────────────┴─────────────┘  ││
│  │                                 ││
│  │   ZES Protocol Processing       ││
│  │   • Frame packing/unpacking     ││
│  │   • CRC8 calculation            ││
│  │   • Function code routing       ││
│  │   • Error handling & retry      ││
│  └─────────────────────────────────┘│
├─────────────────────────────────────┤
│    Embedded FTDI VCP Functionality  │
│    (Integrated USB-Serial Driver)   │
├─────────────────────────────────────┤
│      Windows USB Stack              │
├─────────────────────────────────────┤
│   USB-RS485-WE-1800-BT Converter    │
├─────────────────────────────────────┤
│         RS485 Bus                   │
└─────────────────────────────────────┘
```

**Application Component Roles:**

1. **Embedded FTDI VCP Functionality** (Lower Layer):
   * **Role**: Integrated USB-to-Serial Driver
   * **Responsibility**: USB-to-serial conversion and basic serial communication
   * **Implementation**: Built-in FTDI VCP driver functionality

2. **ZES Protocol Processing Layer** (Upper Layer):
   * **Role**: Protocol Handler – Implements ZES proprietary data link layer protocol
   * **Responsibility**: RS485 protocol handling, buffer management, frame processing, function code routing
   * **Framework**: UMDF 2.0 integrated within application
   * **Interface**: High-level API functions for user applications

### 2.1 Driver-Managed Buffer System

**Buffer Architecture:**
The driver implements a sophisticated buffer management system to handle the asynchronous nature of RS485 communication:

**Uplink Buffer (PC to Device) - Transmitter Buffer:**
- **Capacity**: 5 payload slots × 12 bytes = 60 bytes total
- **Purpose**: Queue outgoing 12-byte payload data from PC to slave devices
- **Management**: FIFO (First-In-First-Out) queue managed by the driver
- **Overflow Prevention**: Mandatory buffer flag checking before each transmission
- **API Behavior**: When buffer is full, API functions return BUFFER_FULL error to prevent data loss
- **Frame-by-Frame Transmission**: Data is sent one frame at a time with buffer availability check

**Downlink Buffer (Device to PC) - Receiver Buffer:**
- **Capacity**: 10 payload slots × 12 bytes = 120 bytes total
- **Purpose**: Store incoming 12-byte payload data from slave devices
- **Management**: FIFO queue with per-slave address organization
- **Data Ready Notification**: Non-blocking check mechanism for data availability
- **Buffer Flag**: Monitored to prevent overflow - checked before storing received payload data

**Frame Structure (16 bytes per frame):**
```cpp
struct RS485Frame {
    uint8_t header;        // 0xAA (1 byte)
    uint8_t id_byte;       // Function code + device address (1 byte)
    uint8_t payload[12];   // Key (4 bytes) + Value (8 bytes) - CORE PROTOCOL DATA
    uint8_t crc8;          // CRC checksum (1 byte)
    uint8_t trailer;       // 0x0D (1 byte)
};  // Total: 16 bytes
```

**Critical Protocol Design Note:**
The **12-byte payload** is the core of the entire RS485 communication protocol. This payload contains the essential Key-Value pair data that enables all communication between PC and slave devices for system configuration, user configuration, application data requests, and AI model data.

**Important Clarification: Protocol Layer Separation**

The driver processes the **complete 16-byte frame** (Header + ID + Payload + CRC + Trailer) including frame validation, error detection, function code routing, and address filtering.

User applications only handle the **12-byte payload** containing:
- **Key (4 bytes)**: Command identifier (e.g., "S001", "U001", "A001")
- **Value (8 bytes)**: Data value (integers, floats, or structured data)

This separation provides simplicity for users, reliability through driver-level error handling, and automatic routing based on function codes.

**Buffer Management Features:**
- **Thread-Safe Operations**: All buffer operations are protected by synchronization mechanisms
- **Fixed-Size Buffers**: Eliminates dynamic memory allocation overhead
- **Real-Time Performance**: Optimized for low-latency communication in airborne environments
- **Non-Blocking Design**: Driver operations never block user threads
- **Buffer Status Monitoring**: Applications can query buffer usage and availability
- **Buffer Overflow Prevention**: Pre-transmission and pre-storage checks with FIFO guarantee and configurable overflow policies

### 2.3 Non-Blocking Communication Flow Design

**Critical Design Principle: Asynchronous Request-Response Pattern**

The RS485 driver implements a two-phase communication pattern:

**Phase 1: Request Transmission (Non-blocking)** - PC sends request and returns immediately
**Phase 2: Response Retrieval (Polling-based)** - PC checks if data is ready and retrieves when available

**Benefits:** Airborne environment compatibility, multi-slave efficiency, system responsiveness, and predictable timing.



### 2.4 Frame Processing Architecture

**Asynchronous Frame Processing Pipeline**

The driver implements a frame processing pipeline that operates independently of user threads, focusing on efficiently extracting and managing the **12-byte payload data**.

**Processing Flow:**
1. **Receive Path**: Hardware → Frame Parser → Payload Extractor → Buffer Check → User Application
2. **Transmit Path**: User Application → Buffer Check → Frame Builder → Hardware

**Buffer Flag Check Process:**
- **Before Transmission**: Check uplink buffer flag for available space
- **Before Storage**: Check downlink buffer flag for incoming payload space
- **FIFO Enforcement**: Maintain strict FIFO ordering for payload data
- **Overflow Prevention**: Apply configured overflow policy when buffer is full

**Frame Processing State Machine:**
The driver uses a state machine to process incoming frames (WAITING_HEADER → READING_ID → READING_PAYLOAD → READING_CRC → READING_TRAILER → FRAME_COMPLETE) and routes completed frames to appropriate API categories based on function codes.



### 2.2 RS485 Electrical Interface

The RS485 interface follows the TIA/EIA-485 standard with differential signaling, supporting up to 1200 meters distance, 10 Mbps data rates, and up to 32 devices on a single half-duplex bus. The USB-RS485-WE-1800-BT FTDI converter interfaces with the RS485 bus, with our RS485 Filter Driver adding protocol intelligence above the FTDI VCP Function Driver.

## 3. API Specification

### 3.0 Communication Flow and Protocol Overview

**Typical Communication Flow:**
1. **S001 Broadcast**: Master assigns slave address (broadcast to address 0)
2. **U-series Commands**: Master configures user settings (targeted to specific slave)
3. **A-series Requests**: Master requests data from slave
4. **Error Handling**: CRC errors trigger automatic retry (up to 3 attempts)

**Key Communication Principles:**
- Master initiates all communication
- Slaves respond within 100ms response window
- Broadcast commands (S-series) require single slave connection
- CRC errors trigger automatic retry mechanism
- Timeout errors may indicate hardware failure

### 3.1 Operation Rules for Avoiding Data Collisions

**Key Rules for Half-Duplex Master-Slave Communication:**
1. **Master Responsibility**: Master initiates all communication and controls bus flow
2. **Response Window**: Slaves must respond within 100ms response window
3. **Transmitter Control**: Slaves enable transmitter only when sending, disable immediately after
4. **Broadcasting Rules**: Only master can broadcast (to address 0x00)
5. **Error Recovery**: Master retries if no response received
6. **Bus Idle State**: RS485 transceiver ensures proper start bit detection when idle

### 3.2 UART Frame Format

The UART communication uses the following frame format:

| Start Bit | Data Bits (0-7) | Parity Bit | Stop Bit |
|:---------:|:---------------:|:----------:|:--------:|
| 1 bit     | 8 bits          | None       | 1 bit    |

Total: 10 bits per byte (1 start + 8 data + 0 parity + 1 stop)

This is commonly referred to as "8N1" format (8 data bits, No parity, 1 stop bit).

### 3.3 ZES Protocol Frame Format

The ZES protocol uses a 16-byte frame format as follows:

| Header | ID Byte | Data Payload (12 bytes) | CRC8 | Trailer |
|:------:|:-------:|:-----------------------:|:----:|:-------:|
| 0xAA   | 1 byte  | Key (4 bytes) + Value (8 bytes) | 1 byte | 0x0D |

**Frame Structure Details:**
- **Header (0xAA)**: Fixed start byte for frame synchronization
- **ID Byte**: Contains function code (3 bits) + device address (5 bits)
- **Data Payload**: 12 bytes containing Key-Value pair in JSON format
  - Key: 4 bytes (ASCII command identifier)
  - Value: 8 bytes (binary data value)
- **CRC8**: Error detection using polynomial $0 x 97=x^{8}+x^{5}+x^{3}+x^{2}+x+1$
- **Trailer (0x0D)**: Fixed end byte for frame termination

**CRC8 Implementation:**
- Covers 13 bytes from ID byte to data payload (excluding header byte)
- Both master and slave must calculate and verify CRC
- On CRC error, a re-send request frame is immediately transmitted
- If header byte is corrupted, frame is automatically dropped (timeout-based recovery)

**CRC8 Calculation Example:**
For a frame with ID `0xE1` and payload `0x57 0x30 0x30 0x31 0x00 0x00 0x00 0x05 0x00 0x00 0x00 0x00`:
1. Input data: 13 bytes (ID + 12-byte payload)
2. Polynomial: $0 x 97=x^{8}+x^{5}+x^{3}+x^{2}+x+1$
3. Initial value: 0x00
4. The CRC8 calculation processes each byte sequentially using the polynomial
5. Result: CRC byte to be placed in the frame

**Error Handling and Retry Mechanism:**
- Upon detecting a CRC error, the receiver (master or slave) sends a re-send request frame (function code 0b000)
- The sender retries up to 3 times maximum
- If all retries fail, the communication is considered failed, and the master logs the error or takes corrective action
- This provides a clear limit and aligns with robust error-handling practices

**Re-send Request Strategy Clarification:**
The ZES protocol defines function code 0b000 as "re-send request" for both master and slave use. The implementation strategy is:

1. **Master-Initiated Retries**: When the master detects a CRC error or timeout, it re-sends the original command (with original function code) rather than sending a separate 0b000 frame. This is simpler and sufficient for most cases.

2. **Slave-Initiated Re-send Requests**: If a slave detects a CRC error in a received frame, it can send a re-send request frame (function code 0b000) to the master. However, the current API design focuses on master-initiated retries for simplicity.

3. **API Implementation**: The driver handles retries automatically by re-transmitting the original command. The 0b000 function code is primarily used internally for protocol compliance but is not directly exposed in the high-level API.

This approach provides robust error recovery while maintaining API simplicity.

#### 3.3.1 ID Byte Structure

The ID byte is structured as follows:

| Function Code (3 bits) | Device Address (5 bits) |
|:----------------------:|:-----------------------:|
| High 3 bits            | Low 5 bits              |

The 5-bit address field allows for a maximum of 31 nodes (30 slaves plus one master). Address 0x00 is reserved for broadcast messages. All AI-SLDAP boards are initialized with a default address of 0x00 during manufacturing.

Function codes:
- 0b111: Assign data (Master use)
- 0b110: Request data (Master use)
- 0b010: Response to Assign (Slave use)
- 0b001: Response to Request (Slave use)
- 0b000: Re-send request (Both use)

#### 3.3.2 Data Payload Structure by Category

The data payload consists of a JSON-style key-value pair as defined in the ZES protocol:

**Key-Value Pair Format:**
- **Key Field**: 4 bytes (higher 4 bytes of payload) - holds the name of the variable in ASCII code
- **Value Field**: 8 bytes (lower 8 bytes of payload) - holds the value of the variable in binary format

**Message Categories (as defined in ZES protocol):**

| Key Code | Category | Used For | Examples |
|----------|----------|----------|----------|
| **"Sxxx"** | System configurable data | Assign AI-SLDAP system parameters | S001 (slave address), S002 (baud rate) |
| **"Uxxx"** | User configurable data | Configure/control how AISL works | U001-U006 (various user settings) |
| **"Axxx"** | Application related data | Information/status reports | A001 (SEL event log), A002 (device status), A003 (firmware version), A004 (system statistics), A005 (current configuration) |
| **"Wxxx"** | Weights of AI model | Transmit weight parameters from PC to AISL | W001 (write), W002 (read) |

**Data Payload Structure by API Category:**

**Category 1: System Configuration (Broadcasting)**
- Key (4 bytes): Command identifier (e.g., "S001", "S002" in ASCII)
- Value (8 bytes): Configuration value to be assigned to the FPGA register

**Category 2: Master to Slave Request**
- Key (4 bytes): Data identifier for the requested information (e.g., "A001", "A002" in ASCII)
- Value (8 bytes): Reserved for future use (typically set to 0)

**Category 3: AI Model Weight and Bias Data**
- Key (4 bytes): Memory address in FRAM (for "W001", "W002" operations)
- Value (8 bytes): Data to be written (for write operations) or length of data to read (for read operations)

#### 3.3.3 User Interaction with Protocol

From the user's perspective, the protocol details are abstracted away. Users interact with the three API categories without needing to know about the ID byte structure or how the data is formatted in the payload. The driver automatically handles the construction of appropriate messages based on the API call, including setting the correct function code and device address in the ID byte.

### 3.4 Windows Driver Interface Structure

The RS485 driver is implemented as a Windows User-Mode Driver Framework (UMDF) driver, providing a native Windows driver interface instead of a traditional C++ class library. Applications interact with the driver through Windows I/O Control (IOCTL) calls using **DeviceIoControl()**.

**Data Exchange Mechanism:**
Data exchange between user-mode applications and the RS485 driver is achieved using **DeviceIoControl()** calls with specific IOCTL codes. This approach provides:

1. **Standard Windows Interface**: Uses the industry-standard Windows driver communication method
2. **Asynchronous I/O Support**: Enables non-blocking operations with overlapped I/O
3. **Buffer Management**: Efficient data transfer through system-managed buffers
4. **Error Handling**: Comprehensive error reporting through Windows error codes

**DeviceIoControl() Implementation Strategy:**
- **API Layer**: The high-level API functions (configureSystemSettings, requestData, etc.) internally use DeviceIoControl() calls
- **Abstraction**: Users interact with the simplified API interface without directly calling DeviceIoControl()
- **Internal Implementation**: Each API function translates to appropriate IOCTL codes and buffer management
- **FIFO Guarantee**: DeviceIoControl() calls are queued and processed in strict FIFO order to maintain data integrity

**Complete Driver Interface Architecture with Function Code Mapping:**
```cpp
// Windows Driver Interface - Application Side
class AI_SLDAP_RS485_DriverInterface {
public:
    // Constructor and basic operations
    AI_SLDAP_RS485_DriverInterface();
    ~AI_SLDAP_RS485_DriverInterface();

    // ===== ERROR HANDLE API (Function Code: 0b000) =====
    // FTDI-style management functions
    ConnectionResult openPort(const std::wstring& devicePath);
    ConnectionResult closePort();
    bool isPortOpen() const;
    PortResult getPortInfo(PortInfo& info);

    // Device enumeration (similar to FTDI FT_ListDevices)
    static EnumerationResult enumerateDevices(std::vector<DeviceInfo>& deviceList);
    static DetectionResult detectMultipleDevices(std::vector<uint8_t>& detectedAddresses);

    // Buffer management - CRITICAL for data integrity
    BufferResult getBufferStatus(BufferStatus& status);
    BufferResult checkUplinkBufferAvailability(bool& isFull);
    BufferResult checkDownlinkBufferAvailability(bool& isFull);
    BufferResult clearBuffer(BufferType bufferType = BufferType::BOTH);
    BufferResult setBufferOverflowPolicy(BufferOverflowPolicy policy);
    BufferResult getBufferCapacity(uint32_t& uplinkFrames, uint32_t& downlinkFrames);

    // Hardware status (similar to FTDI FT_GetStatus)
    HardwareResult getHardwareStatus(HardwareStatus& status);

    // Performance monitoring - provides communication statistics and throughput metrics
    // Returns: frame transmission rates, error rates, buffer utilization, retry counts
    PerformanceResult getPerformanceMetrics(PerformanceMetrics& metrics);

    ConfigResult getBaudRate(uint32_t& currentBaudRate);

    // Line status monitoring - provides real-time RS485 bus and hardware status
    // Returns: signal levels, carrier detect, data set ready, ring indicator status
    LineResult getLineStatus(LineStatus& status);

    // Error handling and retry management
    const char* getErrorString(RS485Error error) const;
    using ErrorCallbackFn = std::function<void(RS485Error error, const char* message)>;
    void registerErrorCallback(ErrorCallbackFn callback);
    void unregisterErrorCallback();

    // ===== MASTER BROADCASTING API (Function Code: 0b111 for S-series) =====
    // Automatic buffer flag checking before transmission
    // commandKey: 4-byte command identifier (e.g., "S001", "S002")
    // value: 8-byte payload data
    ConfigurationResult configureSystemSettings(const std::string& commandKey, uint64_t value);
    VerificationResult verifySystemConfig(const std::string& commandKey, uint64_t expectedValue, bool& isMatching);

    // ===== MASTER ASSIGN DATA API (Function Code: 0b111 for U/W-series) =====
    // Automatic buffer flag checking before transmission
    // commandKey: 4-byte command identifier (e.g., "U001", "U002", etc.)
    // value: 8-byte payload data
    ConfigurationResult configureUserSettings(const std::string& commandKey, uint64_t value);
    ModelDataResult modelDataOperation(uint32_t address, uint8_t data[12], bool isWrite, uint32_t length = 0);

    // ===== MASTER REQUEST API (Function Code: 0b110 for A-series) =====
    // Automatic buffer flag checking before transmission
    // Uses slave address previously set by S001 command
    // dataKey: 4-byte command identifier (e.g., "A001", "A002", etc.)
    RequestResult requestData(const std::string& dataKey, const RequestOptions* options = nullptr);

    // ===== SLAVE RESPONSE API (Function Codes: 0b010 and 0b001) =====
    // Non-blocking design: PC requests data, then separately checks for response availability
    // Step 1: Check if slave has prepared response data (non-blocking)
    ResponseResult checkSlaveDataReady(bool& isDataReady);

    // Step 2: Retrieve prepared response data (only when data is ready)
    // Uses fixed-size array for better performance and memory predictability
    ResponseResult receiveSlaveResponse(uint8_t responseData[12], uint32_t timeout = 100);

    // Response callback registration for asynchronous handling
    // Uses fixed-size array for better performance and cross-platform compatibility
    using ResponseCallbackFn = std::function<void(uint8_t slaveAddress, const uint8_t data[12])>;
    void registerResponseCallback(ResponseCallbackFn callback);
    void unregisterResponseCallback();

    // Buffer threshold monitoring
    BufferResult setBufferThreshold(uint32_t thresholdPercent);
    using BufferThresholdCallbackFn = std::function<void(uint32_t currentUsage, uint32_t totalSize)>;
    void registerBufferThresholdCallback(BufferThresholdCallbackFn callback);

private:
    // Windows driver communication
    HANDLE m_driverHandle;
    std::wstring m_devicePath;

    // IOCTL helper methods with automatic buffer checking
    IOCTLResult sendIOCTL(DWORD ioctlCode, void* inputBuffer, DWORD inputSize,
                        void* outputBuffer, DWORD outputSize, DWORD* bytesReturned = nullptr);

    // Buffer flag checking - called before every transmission
    BufferResult checkBufferBeforeTransmission();
    BufferResult checkBufferBeforeStorage(uint8_t deviceAddress);

    // Thread safety
    std::mutex m_apiMutex;

    // Current slave address for U-series commands
    uint8_t m_currentSlaveAddress;

    // Buffer management state
    BufferOverflowPolicy m_bufferOverflowPolicy;
    uint32_t m_bufferThresholdPercent;
};

// ===== IMPROVED ERROR TYPE DEFINITIONS =====
// Specific result types for different API categories to improve code clarity and maintainability

// Connection and port management results
enum class ConnectionResult {
    SUCCESS,
    PORT_NOT_FOUND,
    PORT_ALREADY_OPEN,
    PORT_ACCESS_DENIED,
    INVALID_PORT_NAME,
    CONNECTION_FAILED,
    TIMEOUT
};

// Device enumeration and detection results
enum class EnumerationResult {
    SUCCESS,
    NO_DEVICES_FOUND,
    SYSTEM_ERROR,
    INSUFFICIENT_BUFFER,
    ACCESS_DENIED
};

enum class DetectionResult {
    SUCCESS,
    MULTIPLE_DEVICES_DETECTED,
    NO_RESPONSE,
    COMMUNICATION_ERROR,
    TIMEOUT
};

// Buffer management results
enum class BufferResult {
    SUCCESS,
    INSUFFICIENT_BUFFER,
    BUFFER_OVERFLOW,
    BUFFER_UNDERFLOW,
    INVALID_BUFFER_TYPE,
    BUFFER_NOT_INITIALIZED
};

// Hardware and performance monitoring results
enum class HardwareResult {
    SUCCESS,
    HARDWARE_NOT_READY,
    HARDWARE_ERROR,
    SENSOR_FAILURE,
    COMMUNICATION_LOST
};

enum class PerformanceResult {
    SUCCESS,
    METRICS_NOT_AVAILABLE,
    INSUFFICIENT_DATA,
    CALCULATION_ERROR
};

enum class LineResult {
    SUCCESS,
    LINE_NOT_READY,
    SIGNAL_ERROR,
    BUS_FAULT,
    ELECTRICAL_FAULT
};

// Configuration and data operation results
enum class ConfigurationResult {
    SUCCESS,
    INVALID_COMMAND,
    INVALID_VALUE,
    DEVICE_NOT_RESPONDING,
    CONFIGURATION_FAILED,
    VERIFICATION_FAILED,
    BUFFER_FULL,
    TIMEOUT
};

enum class VerificationResult {
    SUCCESS,
    MISMATCH_DETECTED,
    VERIFICATION_FAILED,
    DEVICE_NOT_RESPONDING,
    TIMEOUT
};

enum class ModelDataResult {
    SUCCESS,
    INVALID_ADDRESS,
    INVALID_DATA_LENGTH,
    WRITE_FAILED,
    READ_FAILED,
    MEMORY_ERROR,
    TIMEOUT
};

// Request and response results
enum class RequestResult {
    SUCCESS,
    INVALID_REQUEST,
    DEVICE_NOT_RESPONDING,
    REQUEST_TIMEOUT,
    BUFFER_FULL
};

enum class ResponseResult {
    SUCCESS,
    NO_DATA_AVAILABLE,
    INVALID_RESPONSE,
    RESPONSE_TIMEOUT,
    BUFFER_EMPTY,
    BUFFER_FULL,
    CRC_ERROR
};

// Port information results
enum class PortResult {
    SUCCESS,
    PORT_NOT_OPEN,
    INVALID_PORT,
    INFO_NOT_AVAILABLE
};

// Configuration query results
enum class ConfigResult {
    SUCCESS,
    CONFIG_NOT_AVAILABLE,
    INVALID_CONFIG,
    READ_ERROR
};

// IOCTL operation results
enum class IOCTLResult {
    SUCCESS,
    INVALID_IOCTL_CODE,
    BUFFER_TOO_SMALL,
    DEVICE_ERROR,
    OPERATION_FAILED
};

// Driver-specific structures for payload buffer management
struct BufferStatus {
    uint32_t uplinkUsed;        // Used payload slots in uplink buffer (0-5)
    uint32_t uplinkTotal;       // Total uplink buffer capacity (5 payload slots)
    uint32_t downlinkUsed;      // Used payload slots in downlink buffer (0-10)
    uint32_t downlinkTotal;     // Total downlink buffer capacity (10 payload slots)
    uint32_t payloadSize;       // Size per payload slot (12 bytes)
    bool isUplinkFull;          // Uplink buffer full flag
    bool isDownlinkFull;        // Downlink buffer full flag
    bool isOverflowDetected;    // Buffer overflow status
    uint32_t totalBufferBytes;  // Total buffer capacity in bytes (60 + 120 = 180 bytes)
};

enum class BufferType {
    UPLINK,     // PC to device buffer
    DOWNLINK,   // Device to PC buffer
    BOTH        // Both buffers
};

enum class BufferOverflowPolicy {
    TRIGGER_ERROR,   // Return error when buffer is full (RECOMMENDED - prevents data loss)
    DISCARD_OLDEST,  // Discard oldest frame when buffer is full
    DISCARD_NEWEST   // Discard new frame when buffer is full
};

**Frame-by-Frame Transmission Control:**
```cpp
// Example: Sending multiple frames with buffer overflow protection
ConfigurationResult sendMultipleFrames(const std::vector<FrameData>& frames) {
    for (const auto& frame : frames) {
        // Check buffer availability before each frame
        BufferStatus status;
        BufferResult bufferCheck = getBufferStatus(status);
        if (bufferCheck != BufferResult::SUCCESS) {
            return ConfigurationResult::BUFFER_ERROR;
        }

        // If buffer is full, stop transmission and return error
        if (status.isUplinkFull) {
            return ConfigurationResult::BUFFER_FULL;
        }

        // Send frame only if buffer has space
        ConfigurationResult result = sendSingleFrame(frame);
        if (result != ConfigurationResult::SUCCESS) {
            return result;
        }
    }
    return ConfigurationResult::SUCCESS;
}
```
```

**Windows Driver IOCTL Codes:**
```cpp
// IOCTL codes for driver communication
#define IOCTL_RS485_CONFIGURE_SYSTEM    CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_CONFIGURE_USER      CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_REQUEST_DATA        CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_RECEIVE_RESPONSE    CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_MODEL_DATA_OP       CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_GET_BUFFER_STATUS   CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x805, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_RS485_CLEAR_BUFFER        CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_SET_BUFFER_POLICY   CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x807, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_GET_HW_STATUS       CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x808, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_RS485_GET_PERFORMANCE     CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x809, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_RS485_CHECK_BUFFER_FLAGS  CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x80A, METHOD_BUFFERED, FILE_READ_ACCESS)
```

### 3.5 DeviceIoControl() Implementation Details and Memory Space Access

**API Design Philosophy:**
The DeviceIoControl() mechanism is implemented **internally within the API functions** rather than being exposed directly to users. This design provides:

1. **User-Friendly Interface**: Applications use high-level API functions (configureSystemSettings, requestData, etc.)
2. **Internal Implementation**: Each API function internally calls DeviceIoControl() with appropriate IOCTL codes
3. **Abstraction Layer**: Users don't need to understand IOCTL codes or buffer management details
4. **Industry Standard**: Follows standard serial port communication interface patterns

**Memory Space Access via DeviceIoControl:**
DeviceIoControl enables the RS485 driver to access different memory spaces efficiently:

1. **User Application Memory**: Driver can read/write user-provided buffers through IOCTL input/output parameters
2. **Driver Internal Memory**: Driver maintains its own buffer management system for payload data
3. **Hardware Device Memory**: Driver communicates with FTDI hardware through lower-level APIs
4. **Cross-Process Communication**: DeviceIoControl provides secure kernel-mediated data exchange

**Why DeviceIoControl is Ideal for RS485 Communication:**
- **Kernel-Mode Access**: Enables direct hardware communication with proper privilege separation
- **Buffer Management**: Supports both input and output buffers for bidirectional data transfer
- **Asynchronous Operations**: Supports overlapped I/O for non-blocking communication
- **Error Handling**: Provides comprehensive error reporting through Windows error codes
- **Security**: Kernel validates all memory access operations to prevent buffer overflows

**Improved Communication Pattern Example:**
```cpp
// Example: Non-blocking request-response pattern with buffer management
class RS485CommunicationExample {
public:
    // Step 1: Send request (non-blocking)
    RequestResult sendDataRequest(const std::string& dataKey) {
        // Check uplink buffer before sending
        BufferStatus status;
        if (driver.getBufferStatus(status) != BufferResult::SUCCESS) {
            return RequestResult::BUFFER_ERROR;
        }

        if (status.isUplinkFull) {
            return RequestResult::BUFFER_FULL;  // Prevent overflow
        }

        // Send request and return immediately
        return driver.requestData(dataKey);
    }

    // Step 2: Check if response is ready (non-blocking)
    bool checkResponseAvailable() {
        bool isDataReady = false;
        ResponseResult result = driver.checkSlaveDataReady(isDataReady);
        return (result == ResponseResult::SUCCESS && isDataReady);
    }

    // Step 3: Retrieve response data (only when ready)
    ResponseResult getResponseData(uint8_t responseData[12]) {
        return driver.receiveSlaveResponse(responseData, 100);
    }

    // Complete communication example
    bool communicateWithSlave(const std::string& dataKey, uint8_t responseData[12]) {
        // Phase 1: Send request
        RequestResult requestResult = sendDataRequest(dataKey);
        if (requestResult != RequestResult::SUCCESS) {
            return false;
        }

        // Phase 2: Poll for response (with timeout)
        auto startTime = std::chrono::steady_clock::now();
        const auto timeout = std::chrono::milliseconds(1000);

        while (std::chrono::steady_clock::now() - startTime < timeout) {
            if (checkResponseAvailable()) {
                // Phase 3: Retrieve data
                ResponseResult responseResult = getResponseData(responseData);
                return (responseResult == ResponseResult::SUCCESS);
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(10));  // Poll every 10ms
        }

        return false;  // Timeout
    }
};
```

**Original Buffer Management Implementation Example:**
```cpp
// Example: Internal implementation with comprehensive buffer checking
ConfigurationResult AI_SLDAP_RS485_DriverInterface::configureSystemSettings(const std::string& commandKey, uint64_t value) {
    std::lock_guard<std::mutex> lock(m_apiMutex);

    // Step 1: Mandatory buffer flag checking before transmission
    BufferResult bufferCheck = checkBufferBeforeTransmission();
    if (bufferCheck != BufferResult::SUCCESS) {
        return ConfigurationResult::BUFFER_FULL;  // Convert buffer error to configuration error
    }

    // Step 2: Validate function code correspondence (S-series uses 0b111)
    if (!isValidSystemCommand(commandKey)) {
        return ConfigurationResult::INVALID_COMMAND;
    }

    // Step 3: Prepare IOCTL input buffer with 12-byte payload
    struct {
        uint32_t key;           // 4 bytes - command key
        uint64_t value;         // 8 bytes - command value
        uint8_t functionCode;   // Function code 0b111 for assign data
        uint8_t slaveAddress;   // 0x00 for broadcast
        uint8_t reserved[2];    // Padding for alignment
    } inputBuffer = {
        commandKey,
        value,
        0b111,  // Assign data function code
        0x00,   // Broadcast address
        {0, 0}  // Reserved
    };

    // Step 4: Call DeviceIoControl() with buffer management
    DWORD bytesReturned;
    BOOL result = DeviceIoControl(
        m_driverHandle,                    // Device handle
        IOCTL_RS485_CONFIGURE_SYSTEM,      // IOCTL code
        &inputBuffer,                      // Input buffer
        sizeof(inputBuffer),               // Input buffer size
        nullptr,                           // Output buffer (not needed)
        0,                                 // Output buffer size
        &bytesReturned,                    // Bytes returned
        nullptr                            // Overlapped (for async operation)
    );

    if (!result) {
        DWORD lastError = GetLastError();
        return mapWindowsErrorToRS485Error(lastError);
    }

    // Step 5: For broadcast commands, no acknowledgment expected
    // For targeted commands, use separate checkSlaveDataReady() and receiveSlaveResponse()
    return ConfigurationResult::SUCCESS;
}

// Improved buffer checking implementation with overflow prevention
BufferResult AI_SLDAP_RS485_DriverInterface::checkBufferBeforeTransmission() {
    BufferStatus status;
    BufferResult result = getBufferStatus(status);
    if (result != BufferResult::SUCCESS) {
        return result;
    }

    // Critical: Check uplink buffer flag before transmission
    if (status.isUplinkFull) {
        // RECOMMENDED: Always return error to prevent data loss
        return BufferResult::INSUFFICIENT_BUFFER;
    }

    // Check if buffer usage exceeds threshold (early warning)
    uint32_t usagePercent = (status.uplinkUsed * 100) / status.uplinkTotal;
    if (usagePercent >= m_bufferThresholdPercent) {
        // Trigger threshold callback if registered
        if (m_bufferThresholdCallback) {
            m_bufferThresholdCallback(status.uplinkUsed, status.uplinkTotal);
        }
    }

    return BufferResult::SUCCESS;
}

// Frame-by-frame transmission with buffer checking
ConfigurationResult sendFrameWithBufferCheck(const FrameData& frame) {
    // Mandatory buffer check before each frame
    BufferResult bufferCheck = checkBufferBeforeTransmission();
    if (bufferCheck != BufferResult::SUCCESS) {
        return ConfigurationResult::BUFFER_FULL;  // Stop transmission
    }

    // Proceed with frame transmission only if buffer has space
    return transmitSingleFrame(frame);
}

// Function code validation
bool AI_SLDAP_RS485_DriverInterface::isValidSystemCommand(const std::string& commandKey) {
    // S-series commands: S001, S002
    return (commandKey == "S001" || commandKey == "S002");
}
```

**Buffer Flag Management:**
```cpp
// Buffer flag checking mechanism
struct BufferFlags {
    bool uplinkFull;        // Uplink buffer full flag
    bool downlinkFull;      // Downlink buffer full flag
    uint32_t uplinkUsed;    // Current uplink usage (0-5)
    uint32_t downlinkUsed;  // Current downlink usage (0-10)
};

// Check buffer flags before operations
RS485Error checkBufferFlags(BufferFlags& flags);
```

## 4. Detailed API Reference

### 4.0 Error Codes

```cpp
enum class RS485Error {
    SUCCESS = 0,

    // FTDI Driver errors (100-199)
    CONNECTION_ERROR = 100, DEVICE_NOT_FOUND = 101, DEVICE_BUSY = 102,
    PORT_NOT_AVAILABLE = 103, DRIVER_NOT_LOADED = 104, INSUFFICIENT_RESOURCES = 105,
    INVALID_HANDLE = 106, INVALID_BAUD_RATE = 107, INVALID_PARAMETER = 108,

    // Buffer Management errors (150-199)
    INSUFFICIENT_BUFFER = 150, BUFFER_OVERFLOW = 151, BUFFER_UNDERFLOW = 152,
    BUFFER_ALLOCATION_FAILED = 153, INVALID_BUFFER_SIZE = 154,

    // ZES Protocol errors (200-299)
    PROTOCOL_ERROR = 200, CRC_ERROR = 201, TIMEOUT_ERROR = 202,
    FRAME_SYNC_ERROR = 203, RETRY_LIMIT_EXCEEDED = 204, UNSUPPORTED_OPERATION = 205,
    INVALID_COMMAND_KEY = 206, INVALID_SLAVE_ADDRESS = 207, BROADCAST_CONFLICT = 208,

    // Memory operation errors (300-399)
    MEMORY_ACCESS_ERROR = 300, MEMORY_WRITE_FAILED = 301, MEMORY_READ_FAILED = 302,
    INVALID_MEMORY_ADDRESS = 303, MEMORY_RANGE_ERROR = 304, MEMORY_PROTECTION_ERROR = 305,

    // Data handling errors (400-499)
    DATA_FORMAT_ERROR = 400, PAYLOAD_SIZE_ERROR = 401, JSON_PARSE_ERROR = 402, CHECKSUM_MISMATCH = 403,

    // Function Code Processing errors (500-599)
    INVALID_FUNCTION_CODE = 500, FUNCTION_CODE_MISMATCH = 501, RESPONSE_TYPE_ERROR = 502, API_CATEGORY_ERROR = 503
};
```

**Error Handling:**

The error handling system integrates FTDI VCP driver errors with ZES protocol errors. Errors are categorized as:
- **Transient errors** (e.g., `TIMEOUT_ERROR`, `CRC_ERROR`, `DEVICE_BUSY`) may succeed on retry
- **Permanent errors** (e.g., `INVALID_PARAMETER`, `DEVICE_NOT_FOUND`) require user intervention

#### 4.0.1 Error Handling with Retry Logic

For transient errors, applications can implement retry logic with configurable delays and maximum retry counts. The driver also provides error callback registration for asynchronous error notification.




### 4.1 Common Structures

```cpp
// Structure for device information
struct DeviceInfo {
    std::string port;       // COM port name
    std::string description;// Device description
    std::string serialNumber;// Serial number (if available)
};

// Hardware status structure
struct HardwareStatus {
    bool isConnected;           // Connection status
    uint32_t ftdiChipStatus;    // FTDI chip status flags
    uint32_t bufferOverflows;   // Number of buffer overflow events
    uint32_t crcErrors;         // Total CRC errors detected
    uint32_t timeoutErrors;     // Total timeout errors
    double signalStrength;      // RS485 signal strength (if available)
    uint32_t framesSent;        // Total frames transmitted
    uint32_t framesReceived;    // Total frames received successfully
};

// Performance metrics structure
struct PerformanceMetrics {
    double avgLatencyMs;        // Average response latency
    uint32_t bytesPerSecond;    // Throughput in bytes per second
    uint32_t successfulFrames;  // Successful frame transmissions
    uint32_t failedFrames;      // Failed frame transmissions
    uint32_t retryCount;        // Total retries performed
    double frameSuccessRate;    // Success rate percentage
};

// Line status structure
struct LineStatus {
    bool carrierDetect;         // CD signal status
    bool dataSetReady;          // DSR signal status
    bool clearToSend;           // CTS signal status
    bool dataTerminalReady;     // DTR signal status
    bool requestToSend;         // RTS signal status
    uint32_t signalStrength;    // Signal strength (0-100%)
    bool busIdle;               // RS485 bus idle status
    bool hardwareError;         // Hardware error detected
};

```

### 4.2 Windows Driver Implementation Details

The RS485 driver is implemented as a UMDF Filter Driver using Windows Driver Framework components for buffer management, non-blocking I/O, frame processing, and thread synchronization.


**Driver Installation Files:**
- **RS485Driver.inf**: Windows Information File for driver installation
- **RS485Driver.dll**: User-mode driver binary
- **RS485DriverInterface.lib**: Application interface library
- **RS485DriverInterface.h**: Header file for application development

#### 4.2.2 Device Enumeration

The driver provides device enumeration through Windows Device Manager APIs, supporting Windows 10/11 with native device integration and metadata access.

#### 4.2.3 Windows Driver Development and Deployment

**Development Environment:** Visual Studio 2022 with Windows Driver Kit (WDK) for Windows 10/11

**Driver Installation Process:** Development testing, test signing, production signing, and Device Manager integration

The filter driver is installed as an Upper Filter above the existing FTDI VCP driver, maintaining compatibility with existing FTDI driver infrastructure.

#### 4.2.4 Buffer Flag Management and FIFO Guarantee

The RS485 driver implements buffer flag checking to prevent data loss and ensures strict First-In-First-Out ordering. The entire communication protocol revolves around efficient handling of 12-byte payload data.

### 4.3 Error Handle API

The Error Handle API provides detailed error information and handling capabilities, including COM port errors inherited from the FTDI driver.

```cpp
const char* getErrorString(RS485Error error) const
```

**Purpose:** Get a human-readable description of an error code
**Features:** Comprehensive error coverage, detailed descriptions, internationalization support

### 4.4 Windows Driver Connection Management

#### 4.4.1 Constructor

Creates a new instance of the RS485 driver interface for Windows driver communication (Windows 10/11).

```cpp
AI_SLDAP_RS485_DriverInterface()
```

The constructor initializes the driver interface and manages payload buffer allocation (5 uplink × 12 bytes + 10 downlink × 12 bytes).

#### 4.4.2 open

Opens the RS485 connection through Windows driver interface.

```cpp
ConnectionResult open(const std::wstring& devicePath)
```

**Parameters:** devicePath - Windows device path (obtained from device enumeration)
**Return Value:** ConnectionResult::SUCCESS if successful, otherwise an error code

#### 4.4.3 close

Closes the RS485 driver connection.

```cpp
void close()
```

**Parameters:** None
**Return Value:** None
**Remarks:** Releases driver resources and clears remaining payload data in buffers.

#### 4.4.4 isOpen

Checks if the driver connection is open.

```cpp
bool isOpen() const
```

**Return Value:** true if the driver connection is open, false otherwise

#### 4.4.5 Buffer Status Monitoring

Monitor the driver-managed buffer status for both uplink and downlink communications.

```cpp
BufferResult getBufferStatus(BufferStatus& status)
```

**Parameters:** status - Reference to BufferStatus structure
**Return Value:** BufferResult::SUCCESS if successful, otherwise an error code

#### 4.4.6 DeviceIoControl() Implementation

The high-level API functions internally use DeviceIoControl() with buffer flag checking and FIFO guarantee.

**Key Implementation Points:**
- DeviceIoControl() is encapsulated within API functions
- Buffer flag checking prevents overflow
- 12-byte payload focus for core protocol information
- FIFO guarantee with strict ordering
- Comprehensive error handling and thread safety

### 4.5 Master Broadcasting API (System Configuration Commands)

The Master Broadcasting API enables PC applications to send broadcast frames to slave devices for system-level configuration (S-series commands).

```cpp
ConfigurationResult configureSystemSettings(const std::string& commandKey, uint64_t value)
```

**Purpose:** Configure system parameters via broadcasting (S-series commands only)
**Parameters:** commandKey ("S001", "S002"), value (S001: 1-31, S002: baud rates)
**Return Value:** ConfigurationResult indicating success or specific error

**Supported Commands:**
- **S001**: Set RS485 slave address (1-31)
- **S002**: Set baud rate (9600, 19200, 38400, 57600, 115200)

#### 4.5.2 Implementation Details

**Hardware Requirements:**
- Single slave device must be connected to RS485 bus during broadcast operations
- Multiple slaves cause bus collisions during acknowledgment
- Broadcast frames sent to address 0x00 with special acknowledgment mechanism

**Key Features:**
- Targeted communication to specific slave device
- Mandatory acknowledgment mechanism
- Hardware limitation enforcement
- Automatic frame construction and retry handling

### 4.6 Master Assign Data API

The Master Assign Data API assigns data to slave devices, including user configuration parameters (U-series) and AI model weights and bias data (W-series).

#### 4.6.1 User Configuration Commands (U-series)

```cpp
ConfigurationResult configureUserSettings(const std::string& commandKey, uint64_t value)
```

**Purpose:** Configure user parameters on slave devices
**Parameters:** commandKey (e.g., "U001", "U002"), value (8-byte value)
**Return Value:** ConfigurationResult indicating success or error

**Addressing:** Uses slave address previously set by S001 command. Set S001 before using U-series commands to avoid broadcasting to all slaves.

**Supported Commands:**
- **U001**: SEL detection threshold (40-500 mA)
- **U002**: SEL maximum amplitude threshold (1000-2000 mA)
- **U003**: SEL detections before power cycle (1-5)
- **U004**: Power cycle duration (200, 400, 600, 800, 1000 ms)
- **U005**: GPIO input functions (channel/enable)
- **U006**: GPIO output functions (channel/enable)

#### 4.6.2 Configuration Verification

```cpp
VerificationResult verifySystemConfig(const std::string& commandKey, uint64_t expectedValue, bool& isMatching)
```

**Purpose:** Verify that a configuration value matches the expected value
**Parameters:** commandKey, expectedValue, isMatching (reference to boolean result)
**Return Value:** VerificationResult indicating success or error

#### 4.6.3 AI Model Weight and Bias Data Commands (W-series)

```cpp
ModelDataResult modelDataOperation(uint32_t address, std::vector<uint8_t>& data, bool isWrite, uint32_t length = 0)
```

**Purpose:** Manage AI model weights and bias data stored in FRAM memory
**Parameters:** slaveAddress (1-31), address (FRAM memory address), data (vector), isWrite (operation type), length (for reads)
**Return Value:** ModelDataResult indicating success or error

**Supported Commands:**
- **W001**: Write model data to FRAM
- **W002**: Read model data from FRAM

### 4.7 Master Request API

The Master Request API enables PC applications to send requests to slave devices and receive responses without blocking the application thread.

```cpp
RequestResult requestData(const std::string& dataKey, const RequestOptions* options = nullptr)
```

**Purpose:** Request information from slave devices
**Parameters:** dataKey (A-series commands), options (filtering, pagination)
**Return Value:** RequestResult indicating success or error

**Key Features:**
- Non-blocking design with immediate return after acknowledgment
- Asynchronous operation using OS threads
- Data retrieval separation using receiveSlaveResponse function
- Parameterized requests with filtering options

**RequestOptions Structure:** Supports time filtering, pagination, event type filtering, and format options for A001/A004 JSON responses.
#### 4.7.2 Optional Callback Mechanism

The driver provides optional callback mechanism for notification when response data is available, eliminating the need to poll using `receiveSlaveResponse`.

### 4.8 Slave Response API

The Slave Response API enables PC applications to receive data from slave devices using a two-phase non-blocking approach.

**Phase 1: Check Data Availability**
```cpp
ResponseResult checkSlaveDataReady(bool& isDataReady)
```

**Phase 2: Retrieve Response Data**
```cpp
ResponseResult receiveSlaveResponse(uint8_t responseData[12], uint32_t timeout = 100)
```

**Design Benefits:**
- No thread blocking for responsive applications
- Efficient multi-slave polling
- Predictable memory with fixed-size arrays
- Real-time compatible for airborne applications

**Key Features:**
- FIFO buffer system prevents data loss
- Data ready notification system
- Multi-frame response handling
- Intelligent data alignment for different response sizes
   - Driver reassembles the complete response automatically

**Implementation Notes:**
- Automatic buffer management with overflow protection
- Multi-frame responses reassembled transparently
- Frame sequencing handled automatically by driver

**Response Handling Approaches:**

The API supports two main approaches:

1. **Callback-Based Approach**: Event-driven, best for real-time monitoring and GUI applications
2. **Polling-Based Approach**: Sequential, best for simple scripts and ordered data processing
3. **Blocking Approach**: Not recommended for airborne systems


#### 4.8.2 Buffer Configuration and Management

The driver provides comprehensive buffer management capabilities for airborne environments.

**Buffer Configuration:**
- Default 256KB buffer for typical applications
- 512KB buffer for high data volume or slow polling
- Configurable during driver initialization

**Buffer Management API:**
- Buffer size and usage monitoring
- Per-slave pending response counts
- Buffer clearing and threshold management
- Overflow policies (discard oldest/newest, trigger error)
- FIFO buffer system with thread-safe implementation

#### 4.8.3 Thread Safety

The Slave Response API is thread-safe, allowing multiple threads to call `receiveSlaveResponse` concurrently. Callbacks are invoked on separate threads, so callback handlers should be thread-safe.

#### 4.8.4 Relationship with Master Request API

The Slave Response API works with the Master Request API to form a complete non-blocking communication system:
- Request-response pairing with data retrieval separation
- Non-blocking design for airborne environments
- Asynchronous operation with background OS threads
- Buffer management for response storage
- Callback alternative for event-driven approach

## 5. Using the API

### 5.1 Supported Operating Systems and API Consistency

**Cross-Platform Support:**
- **Windows** (10, 11) - Windows Driver Kit (WDK) implementation
- **Linux** (Ubuntu 18.04+, CentOS 7+, RHEL 8+) - Linux kernel module implementation

**API Design Consistency:**
The RS485 driver API is identical across Windows and Linux platforms with consistent interface, data format, error handling, and buffer management. Platform differences are limited to device paths and installation methods.

### 5.1.1 Platform Differences

**Key Differences:**
- **Device Paths**: Windows uses COM ports (`\\.\COM3`), Linux uses `/dev/ttyUSB0`
- **Installation**: Windows uses INF files, Linux uses kernel modules
- **Permissions**: Windows requires administrator, Linux requires `dialout` group membership
- **Implementation**: Windows uses DeviceIoControl(), Linux uses ioctl() system calls

### 5.1.2 Cross-Platform Data Format Standardization

**Universal Data Compatibility:**
The 12-byte payload format uses standardized data representation:
- **Integer Data**: 32-bit little-endian format
- **Floating-Point**: IEEE 754 standard (universally supported)
- **Endianness**: Little-endian format (x86/x64 native)
- **Cross-Language**: Consistent encoding across C++, Python, Java



### 5.2 Installation

#### 5.2.1 FTDI VCP Driver Installation
1. Download driver from https://ftdichip.com/drivers/vcp-drivers/
2. Install according to OS instructions
3. Connect USB-RS485-WE-1800-BT adapter
4. Verify device appears as virtual COM port

#### 5.2.2 ZES Driver Installation Options
- **Option 1**: OS I/O Thread Integration (device driver)
- **Option 2**: Independent Thread Operation (background service) - **Recommended**

#### 5.2.3 ZES Driver Requirements
Core functionalities include data link layer implementation, RS485 bus control, transmission error handling, buffer and FIFO functions, and five-category API interface with thread-safe non-blocking design.

### 5.3 Windows Driver Quick Start Guide

#### 5.3.1 Basic Usage Steps

1. **Enumerate Devices**: Use `enumerateDevices()` to find available RS485 devices
2. **Connect**: Open connection using `open(devicePath)`
3. **Configure System**: Set slave address using `configureSystemSettings("S001", address)`
4. **Configure User Settings**: Set parameters using `configureUserSettings()`
5. **Request Data**: Use `requestData()` for A-series commands
6. **Receive Responses**: Use `receiveSlaveResponse()` or register callbacks
7. **Clean Up**: Close connection using `close()`

#### 5.3.2 Error Handling
- Register error callbacks for transient vs. permanent error categorization
- Configure buffer overflow policies (discard oldest/newest, trigger error)
- Monitor buffer status to prevent overflow

### 5.3 API Usage Examples

#### 5.3.1 Basic Usage Pattern

```cpp
// 1. Enumerate and connect
std::vector<DeviceInfo> devices;
AI_SLDAP_RS485_Driver::enumerateDevices(devices);
AI_SLDAP_RS485_Driver driver(devices[0].port);
driver.open();

// 2. Configure system (S-series)
driver.configureSystemSettings("S001", 5);  // Set slave address

// 3. Configure user settings (U-series)
driver.configureUserSettings("U001", 250);  // SEL threshold

// 4. Request data (A-series)
driver.requestData("A001");  // SEL event log
uint8_t responseData[12];
driver.receiveSlaveResponse(5, responseData, 200);

// 5. Clean up
driver.close();
```
#### 5.3.2 Error Handling

The API provides comprehensive error handling with specific result types for different operations and categorization of transient vs. permanent errors.

## 6. Additional Features

### 6.1 Performance Metrics and Optimization

The API provides performance monitoring capabilities including:

**PerformanceMetrics Structure:**
- Average latency, throughput, success rates
- CRC errors, timeouts, buffer overflow counts
- Bus utilization and response time analysis

**LineStatus Structure:**
- Signal strength, noise level, hardware error detection
- Bus idle status and last activity monitoring

**Performance Optimization Guidelines:**
- Monitor frame success rates (target: >95%)
- Use higher baud rates for shorter cable runs
- Consider larger buffers for high-volume transfers

### 6.2 Hardware Features

| Feature | Command | Description | API Call Example |
|---------|---------|-------------|------------------|
| **SEL Protection** | U002, U003 | Configurable amplitude threshold (1000-2000mA) and detection count (1-5) | `configureUserSettings('U002', 1500)` |
| **GPIO Inputs** | U005 | 2 channels that can pause power cycling when high (disabled by default) | `configureUserSettings('U005', 0x0100)` |
| **GPIO Outputs** | U006 | 2 channels that go high during power cycling (disabled by default) | `configureUserSettings('U006', 0x0101)` |

### 6.3 GUI Integration

The RS485 driver includes a GUI application for easy configuration:

- Visual interface for all system configuration parameters
- Real-time status monitoring
- Device discovery and connection management

## 7. Windows Driver Implementation Summary

### 7.1 Key Changes: Filter Driver Architecture

**Architecture Changes:**
1. **Driver Type**: Implemented as UMDF 2.0 **Filter Driver** (not replacing FTDI VCP)
2. **Driver Stack Position**: **Upper Filter** above FTDI VCP Function Driver
3. **Buffer Management**: Implemented driver-managed fixed-size payload buffers (5 uplink × 12 bytes + 10 downlink × 12 bytes)
4. **Protocol Processing**: Added RS485/ZES protocol intelligence on top of basic serial communication
5. **Communication Method**: IOCTL-based high-level API while leveraging FTDI VCP for hardware communication

**Technical Benefits:**
- **Leverages Existing Infrastructure**: Uses proven FTDI VCP drivers as foundation
- **Enhanced Functionality**: Adds RS485 protocol processing and intelligent buffering
- **Maintains Compatibility**: Existing FTDI driver infrastructure remains intact
- **Improved Debugging**: Visual Studio integration with WDK debugging tools
- **Professional Deployment**: Standard Windows filter driver installation and signing process

**Buffer Management Advantages:**
- **Fixed Memory Allocation**: Eliminates dynamic memory allocation overhead
- **Real-Time Performance**: Optimized for low-latency airborne environments
- **Thread-Safe Operations**: Built-in synchronization for concurrent access
- **Overflow Protection**: Configurable policies for buffer overflow handling

### 7.2 Application Deliverable

**Final Deliverable:**

**RS485_Communication_Application.exe**: A complete Windows application that provides:

1. **Integrated FTDI VCP Driver Functionality**: Built-in support for USB-RS485-WE-1800-BT converter without requiring separate FTDI driver installation
2. **ZES Protocol Implementation**: Complete implementation of the ZES proprietary data link layer protocol as specified in the guidance document
3. **User-Mode Driver Framework (UMDF 2)**: Windows-native driver implementation for reliable RS485 communication
4. **Advanced Buffer Management**: Driver-managed frame buffers with intelligent overflow handling
5. **High-Level API Interface**: Five categories of APIs that abstract away protocol complexity
6. **Error Handling and Recovery**: Comprehensive error management with automatic retry mechanisms
7. **Configuration Management**: Persistent storage of device configurations in FRAM
8. **Multi-Device Support**: Support for up to 30 slave devices on a single RS485 bus

**Architecture Overview:**
The application combines Windows User-Mode Driver Framework (UMDF 2) with integrated FTDI VCP driver functionality to create a single executable that handles all aspects of RS485 communication with AI-SLDAP devices. This approach eliminates the need for separate driver installations while providing enterprise-grade reliability and performance.
5. **Test Applications**: Comprehensive test suite for validation
6. **Documentation**: Updated implementation guides and API documentation

### 7.3 Migration Benefits Summary

**For Developers:**
- Simplified installation process (no third-party driver dependencies)
- Enhanced debugging capabilities with Visual Studio WDK integration
- Better error handling and diagnostics through Windows driver framework
- Professional driver signing and deployment process

**For End Users:**
- Automatic driver installation through Windows Update (when signed)
- Better system stability and reliability
- Native Windows Device Manager integration
- Improved performance and lower latency

**For System Integration:**
- Standardized Windows driver architecture
- Better compatibility with Windows security policies
- Enhanced support for enterprise deployment scenarios
- Future-proof design aligned with Windows driver development trends

## 9. Critical Technical Implementation Highlights

### 9.1 Non-Blocking Driver Operation: Never Hold User Threads

**Problem Statement:**
How does the driver ensure that user application threads are never blocked during RS485 operations, especially in real-time airborne environments?

**Solution Architecture:**

**1. Asynchronous I/O Completion Model**
```cpp
// User thread makes IOCTL call
NTSTATUS ProcessIOCTLRequest(IWDFIoRequest* pRequest) {
    // NEVER block here - immediately queue request and return
    NTSTATUS status = QueueRequestForAsyncProcessing(pRequest);

    if (NT_SUCCESS(status)) {
        // Return STATUS_PENDING - user thread continues immediately
        return STATUS_PENDING;
    }

    // Only return synchronously for immediate errors
    return status;
}

// Processing happens in driver-managed work items
void ProcessRequestAsync(IWDFIoRequest* pRequest) {
    // This runs in driver context, not user thread
    // Perform actual RS485 communication
    // Complete request when done
    pRequest->Complete(STATUS_SUCCESS);
}
```

**2. Driver-Managed Thread Pool**
- **Work Items**: All complex processing occurs in driver work items
- **DPC Context**: Frame parsing happens at DPC level for minimal latency
- **Completion Callbacks**: User applications are notified via completion callbacks
- **No Blocking Primitives**: Driver never uses blocking waits or sleeps

**3. Request Queuing Strategy**
```cpp
class RequestManager {
private:
    WDFQUEUE m_pendingRequests;     // Queue for user requests
    WDFWORKITEM m_processorWorkItems[4];  // 4 parallel processors

public:
    // User thread context - returns immediately
    NTSTATUS QueueRequest(IWDFIoRequest* pRequest) {
        WdfIoQueueAdd(m_pendingRequests, pRequest);
        WdfWorkItemEnqueue(m_processorWorkItems[GetNextProcessor()]);
        return STATUS_PENDING;  // User thread continues
    }

    // Driver work item context - processes requests asynchronously
    void ProcessQueuedRequests() {
        IWDFIoRequest* pRequest;
        while (WdfIoQueueRetrieveNextRequest(m_pendingRequests, &pRequest) == STATUS_SUCCESS) {
            ProcessSingleRequest(pRequest);
            pRequest->Complete(STATUS_SUCCESS);
        }
    }
};
```

**4. Benefits for Airborne Systems**
- **Real-Time Responsiveness**: User applications remain responsive
- **Deterministic Behavior**: No unpredictable blocking delays
- **Scalability**: Multiple applications can use driver simultaneously
- **System Stability**: No thread starvation or deadlock risks

### 9.2 Frame Processing: Efficient State Machine Implementation

**Problem Statement:**
How does the driver efficiently process incoming RS485 frames byte-by-byte without blocking and handle frame synchronization, CRC validation, and error recovery?

**Solution Architecture:**

**1. Multi-Level Processing Pipeline**
```
Level 1: DPC Context (Minimal Latency)
├── Byte-by-byte state machine
├── Frame boundary detection
└── Basic validation

Level 2: Work Item Context (Complex Processing)
├── CRC8 validation
├── Protocol interpretation
├── Buffer management
└── Error handling
```

**2. State Machine Implementation**
```cpp
// Called from DPC - processes one byte in microseconds
FrameProcessResult ProcessIncomingByte(uint8_t byte) {
    // State machine with minimal processing per byte
    switch (m_currentState) {
        case WAITING_HEADER:
            if (byte == 0xAA) {
                m_frameBuffer[0] = byte;
                m_bytesReceived = 1;
                m_currentState = READING_ID;
            }
            return CONTINUE;  // Never blocks

        case READING_PAYLOAD:
            m_frameBuffer[m_bytesReceived++] = byte;
            if (m_bytesReceived == 14) {
                m_currentState = READING_CRC;
            }
            return CONTINUE;  // Process next byte

        case READING_TRAILER:
            if (byte == 0x0D && m_bytesReceived == 16) {
                return FRAME_READY;  // Schedule work item
            } else {
                ResetStateMachine();
                return FRAME_ERROR;
            }
    }
}
```

**3. Frame Validation and Error Recovery**
```cpp
// Called from work item - can perform complex operations
void ProcessCompleteFrame(const RS485Frame& frame) {
    // CRC8 validation
    uint8_t calculatedCRC = CalculateCRC8(&frame.id_byte, 13);
    if (calculatedCRC != frame.crc8) {
        // Non-blocking error recovery
        ScheduleResendRequest(frame);
        return;
    }

    // Route to appropriate buffer based on function code
    uint8_t functionCode = (frame.id_byte >> 5) & 0x07;
    uint8_t deviceAddress = frame.id_byte & 0x1F;

    // Store in ring buffer and notify waiting applications
    StoreInDownlinkBuffer(frame, deviceAddress);
    NotifyWaitingApplication(deviceAddress);  // Async notification
}
```

**4. Performance Characteristics**
- **DPC Processing**: < 10 microseconds per byte
- **Frame Completion**: < 100 microseconds for complete frame processing
- **Buffer Design**: Fixed 12-byte payload buffer slots (no frame overhead stored)
- **Error Recovery**: Automatic retry without user intervention
- **Throughput**: Supports full RS485 bandwidth (up to 115200 baud)

**5. Synchronization Strategy**
```cpp
// Lock-free design for maximum performance
class FrameBuffer {
private:
    volatile LONG m_writeIndex;
    volatile LONG m_readIndex;
    RS485Frame m_frames[BUFFER_SIZE];

public:
    // Producer (DPC context) - lock-free
    bool PushFrame(const RS485Frame& frame) {
        LONG currentWrite = m_writeIndex;
        LONG nextWrite = (currentWrite + 1) % BUFFER_SIZE;

        if (nextWrite == m_readIndex) {
            return false;  // Buffer full
        }

        m_frames[currentWrite] = frame;
        InterlockedExchange(&m_writeIndex, nextWrite);
        return true;
    }

    // Consumer (user context) - lock-free
    bool PopFrame(RS485Frame& frame) {
        LONG currentRead = m_readIndex;
        if (currentRead == m_writeIndex) {
            return false;  // Buffer empty
        }

        frame = m_frames[currentRead];
        InterlockedExchange(&m_readIndex, (currentRead + 1) % BUFFER_SIZE);
        return true;
    }
};
```

**Key Technical Features:**
1. **Zero User Thread Blocking**: All operations return immediately or use async completion
2. **Frame Processing**: State machine processes frames with minimal CPU overhead
3. **Lock-Free Design**: Ring buffers use atomic operations instead of locks
4. **Automatic Error Recovery**: CRC errors and timeouts handled transparently
5. **Real-Time Capability**: Suitable for airborne and industrial control systems

## 8. Frequently Asked Questions (FAQ)

### 8.1 Windows Driver Questions

**Q: What are the system requirements for the Windows Driver Kit (WDK) based RS485 driver?**
A: The driver requires Windows 10 or later (64-bit), Visual Studio 2022 with WDK extension for development, and appropriate USB-RS485 converter hardware. The driver is digitally signed for production deployment.

**Q: How does the driver-managed buffer system work?**
A: The driver maintains fixed-size payload buffers: 5 payload slots (60 bytes) for uplink (PC to device) and 10 payload slots (120 bytes) for downlink (device to PC). Each payload slot stores exactly 12 bytes of meaningful data. The driver automatically manages these buffers with configurable overflow policies.

**Q: Can I use the old FTDI VCP driver alongside the new Windows filter driver?**
A: Yes! In fact, our RS485 filter driver **requires** the FTDI VCP driver to be present. The FTDI VCP driver serves as the Function Driver (lower layer) that handles the basic USB-to-serial conversion, while our RS485 filter driver sits above it as an Upper Filter Driver (upper layer) to provide RS485 protocol processing and advanced buffering.

**Q: How do I install the Windows driver?**
A: For development: Use Visual Studio's driver deployment features. For production: Install the signed driver package (.inf file) through Device Manager or use the provided installer. The driver will appear in Windows Device Manager under "Ports" or a custom device category.

**Q: What happens if the driver buffer overflows?**
A: The driver supports three overflow policies: DISCARD_OLDEST (default), DISCARD_NEWEST, or TRIGGER_ERROR. Applications can monitor buffer status and configure the policy based on their requirements.

**Q: How does the Filter Driver communicate with the FTDI VCP Function Driver?**
A: The RS485 Filter Driver sits in the Windows driver stack above the FTDI VCP Function Driver. When applications send IOCTL requests to our filter driver, it processes the RS485 protocol logic (frame packing, CRC calculation, etc.) and then forwards the processed serial data to the FTDI VCP driver using standard Windows I/O mechanisms (IRP forwarding). Similarly, data received from the FTDI driver is processed by our filter driver before being returned to the application.

**Q: How does the driver ensure it never blocks user application threads?**
A: The driver uses a sophisticated asynchronous I/O model where all IOCTL calls return immediately with STATUS_PENDING. Actual processing occurs in driver-managed work items and DPC contexts. User threads never wait for RS485 operations to complete - instead, they receive completion notifications through callbacks or can poll for results. This is critical for real-time airborne systems where thread blocking is unacceptable.

**Q: How does the driver process RS485 frames efficiently?**
A: The driver implements a multi-level frame processing pipeline: (1) DPC context handles byte-by-byte state machine processing in microseconds, detecting frame boundaries and basic validation; (2) Work item context performs complex operations like CRC8 validation, protocol interpretation, and buffer management. The state machine processes one byte at a time without blocking, using lock-free ring buffers for maximum performance. This design supports full RS485 bandwidth while maintaining real-time responsiveness.

### 8.2 Cross-Platform and Data Format Questions

**Q: Is the API design consistent with typical Linux system APIs?**
A: Yes, the RS485 driver API follows standard Linux patterns:
- **Device Enumeration**: Similar to `/dev/ttyUSB*` enumeration used by other serial libraries
- **Error Handling**: Uses standard error code patterns similar to Linux system calls
- **Threading**: Compatible with pthread and standard Linux threading models
- **Permissions**: Follows standard Linux device permission models (dialout group)
- **Installation**: Uses standard kernel module installation procedures

The high-level API functions are identical between Windows and Linux, with only platform-specific implementation details differing (device paths, internal system calls).

**Q: How does the data format ensure compatibility across different systems and programming languages?**
A: The API uses universal standards to ensure cross-platform compatibility:

1. **Byte Order**: Little-endian format (native to x86/x64, consistent across platforms)
2. **Integer Format**: 32-bit unsigned integers (standard across C++, Python, Java, C#)
3. **Floating-Point**: IEEE 754 standard (universally supported)
4. **Padding**: Explicit zero-padding prevents data corruption
5. **Validation**: Helper functions ensure correct encoding/decoding

Example showing identical results across platforms:
```cpp
// C++ (Windows/Linux):
uint32_t value = 1500;
uint64_t payload = RS485DataFormat::encodeInteger(value);
// Result: 0x00000000000005DC

// Python (Windows/Linux):
import struct
value = 1500
payload_bytes = struct.pack('<I', value) + b'\x00\x00\x00\x00'
payload = struct.unpack('<Q', payload_bytes)[0]
# Result: 0x00000000000005DC (identical)
```

**Q: How do I ensure my integer and floating-point data is transmitted correctly across different systems?**
A: Follow these guidelines for reliable cross-platform data transmission:

1. **For Integers**: Always use the helper functions and validate ranges:
```cpp
// Safe integer encoding (works on all platforms)
if (RS485DataFormat::validateIntegerRange(value, 40, 500)) {
    uint64_t payload = RS485DataFormat::encodeInteger(value);
    driver.configureUserSettings("U001", payload);
}
```

2. **For Floating-Point**: Use IEEE 754 standard with validation:
```cpp
// Safe float encoding (works on all platforms)
float weight = 3.14159f;
if (RS485DataFormat::validateFloatRange(weight, -1000.0f, 1000.0f)) {
    uint64_t payload = RS485DataFormat::encodeFloat(weight);
    driver.configureUserSettings("W001", payload);
}
```

3. **For Future Decimal Support**: The 8-byte payload can accommodate:
   - **Single precision float**: 32-bit IEEE 754 (±3.4 × 10^38, ~7 decimal digits)
   - **Double precision float**: 64-bit IEEE 754 (±1.7 × 10^308, ~15 decimal digits)
   - **Fixed-point decimal**: 32-bit scaled integer (user-defined precision)

**Q: What happens if I send data from a Windows system to a Linux system or vice versa?**
A: The data format is designed to be completely interoperable:

1. **Same Wire Format**: Both systems use identical 12-byte payload structure
2. **Same Byte Order**: Little-endian format is consistent across x86/x64 platforms
3. **Same Standards**: IEEE 754 floating-point works identically on both platforms
4. **Validation**: Helper functions ensure data integrity across platform boundaries

Example of cross-platform communication:
```cpp
// Windows system sends:
driver.configureUserSettings("U001", 250);  // Threshold = 250mA

// Linux system receives and processes identically:
uint8_t responseData[12];
driver.receiveSlaveResponse(5, responseData, 200);
// Decodes to exactly 250mA regardless of platform
```

### 8.3 General Questions

**Q: What is the default slave address used if no S001 command has been executed?**
A: The default slave address is 0x00 (broadcast address). However, it's recommended to always explicitly set the slave address using S001 before sending U-series commands.

**Q: Can I connect multiple slave devices when using the Master Broadcasting API?**
A: No. Due to hardware limitations, only one slave device should be connected when using the Master Broadcasting API (S-series commands). Multiple slaves responding simultaneously would cause bus collisions.

**Q: What if I get no response during broadcasting operations?**
A: If you're not getting responses during broadcasting:
1. Ensure only one slave device is connected to the RS485 bus
2. Check cable integrity and connections
3. Verify power to the slave device
4. Try a lower baud rate temporarily to rule out timing issues
5. Check termination resistors on the RS485 bus
6. Use the `detectMultipleDevices()` function to check for multiple devices

**Q: How do I handle communication errors?**
A: The driver provides comprehensive error handling through error codes and error callbacks. You can register an error callback to be notified of errors asynchronously, or check the return value of each API call.

**Q: How do I distinguish between transient and permanent errors?**
A: The driver categorizes errors into two types:
- **Transient errors**: May resolve with retries (e.g., TIMEOUT_ERROR, CRC_ERROR, DEVICE_BUSY)
- **Permanent errors**: Require user intervention (e.g., INVALID_PARAMETER, DEVICE_NOT_FOUND)

You can implement retry logic for transient errors:
```cpp
// Example retry logic
if (isTransientError(error)) {
    for (int i = 0; i < 3; i++) {
        // Wait and retry
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        status = driver.requestData(dataKey);
        if (status == RS485Error::SUCCESS) break;
    }
}
```

See section 4.0.1 for a complete implementation example.

**Q: Is the driver thread-safe?**
A: Yes, the driver is designed to be thread-safe. You can call API functions from multiple threads without worrying about race conditions. The driver uses internal synchronization to prevent conflicts.

### 8.2 Configuration and Addressing

**Q: How do I configure multiple slave devices?**
A: To configure multiple slaves, you need to:
1. Set the slave address using S001 (e.g., `configureSystemSettings("S001", 1)`)
2. Configure that slave using U-series commands via `configureUserSettings`
3. Change the slave address using S001 again (e.g., `configureSystemSettings("S001", 2)`)
4. Configure the next slave using U-series commands via `configureUserSettings`
5. Repeat for each slave

**Q: Why are there separate functions `configureSystemSettings` and `configureUserSettings` instead of a single function?**
A: The separation provides clearer distinction between S-series commands (system settings via broadcasting) and U-series commands (user settings to specific slaves). This design makes the API more intuitive, prevents misuse, and better reflects the underlying protocol differences between these command types.

**Q: Do S001 and U001 commands need to be differentiated even if we only have one slave device?**
A: Yes, they should still be differentiated for several reasons:
1. S001 uses broadcasting (address 0x00) while U001 targets a specific slave address
2. S001 sets the addressing context for subsequent U-series commands
3. The separation maintains consistency with the ZES protocol design
4. It provides better code clarity and maintainability
5. It allows for future expansion if additional slave devices are added later

**Q: Why does the API use JSON format for A001 and A004 commands?**
A: JSON format is used for several industry-standard reasons:
1. **Flexibility**: JSON can represent complex nested data structures without requiring fixed schemas
2. **Self-describing**: The format is human-readable and self-documenting
3. **Extensibility**: New fields can be added without breaking backward compatibility
4. **Widespread Support**: JSON parsing libraries are available in virtually all programming languages
5. **Industry Standard**: Modern serial communication protocols increasingly use JSON for complex data
6. **Compact Format**: For complex data, JSON can be more compact than fixed binary formats with many optional fields

**Q: What is the purpose of the RequestOptions structure in the requestData API?**
A: The RequestOptions structure follows industry-standard patterns for serial communication APIs by providing:
1. **Parameterized Requests**: Allows filtering and limiting data without creating numerous specialized functions
2. **Pagination Support**: Enables retrieving large datasets in manageable chunks (offset + maxRecords)
3. **Time-Based Filtering**: Particularly useful for event logs to retrieve events within specific time ranges
4. **Type Filtering**: Allows requesting only specific types of events or data
5. **Format Control**: Provides options for controlling response format (e.g., compact JSON)
6. **Optional Usage**: The parameter is optional, maintaining backward compatibility and simplicity for basic requests
7. **Extensibility**: The structure can be extended with new fields without breaking existing code

### 8.3 Response Handling and Performance

**Q: When should I use callbacks versus polling for response handling?**
A: Choose based on your application needs:
- **Use callbacks when**:
  - Building event-driven applications (e.g., GUI interfaces)
  - Handling real-time monitoring where responses need immediate processing
  - Working with asynchronous workflows where you don't want to block execution

- **Use polling when**:
  - Implementing sequential workflows where order matters
  - Writing simpler scripts without callback complexity
  - Working in environments where threading is limited

- **Use blocking calls when**:
  - Writing the simplest possible code
  - In scripts where blocking is acceptable
  - When you need guaranteed sequential processing

See section 4.7.1 for implementation examples of each approach.

**Q: How do I prevent buffer overflow during long operations?**
A: You can:
1. Set a buffer threshold and register a callback to be notified when the buffer usage exceeds that threshold
2. Configure the buffer overflow policy to determine how overflow situations are handled
3. Regularly call `receiveSlaveResponse` to process data from the buffer
4. Use a dedicated thread for handling responses

**Q: What is the maximum transmission speed supported?**
A: The driver supports baud rates up to 115200 bps. However, the actual throughput may be lower due to the protocol overhead and the half-duplex nature of RS485.

**Q: How can I optimize performance when working with multiple slave devices?**
A: For optimal performance:
1. Use non-blocking operations with callbacks to handle responses asynchronously
2. Implement a dedicated response handling thread
3. Batch commands where possible to reduce protocol overhead
4. Use appropriate buffer sizes based on expected data volumes
5. Consider using higher baud rates for shorter cable runs
6. Implement intelligent retry logic for transient errors

## 9. Conclusion

The AI-SLDAP RS485 Driver API provides a comprehensive solution for communicating with AI-SLDAP devices over RS485, implementing the ZES protocol with key enhancements for reliability and performance.

### 9.1 API Summary

| ZES Driver API Category | API Function | Purpose | Command Series |
|-------------------------|--------------|---------|----------------|
| **Error Handle API** | `getErrorString(error)` | Provide detailed error information with transient vs. permanent categorization | N/A |
| **Master Broadcasting API** | `configureSystemSettings(commandKey, value)` | Configure system parameters via broadcasting | S-series |
| **Master Assign Data API** | `configureUserSettings(commandKey, value)` | Configure user parameters on slave devices | U-series |
| **Master Assign Data API** | `modelDataOperation(address, data, isWrite, length)` | Manage AI model data in FRAM memory | W-series |
| **Master Request API** | `requestData(dataKey, options)` | Request information from slaves with non-blocking operation | A-series |
| **Slave Response API** | `receiveSlaveResponse(responseData, timeout)` | Receive data with fixed-size payload buffer management (180 bytes total) | N/A |

### 9.2 Key Design Features

- **Cross-Platform API Consistency**: Identical API interface on Windows and Linux platforms, following industry-standard patterns
- **Universal Data Format**: IEEE 754 and little-endian standards ensure compatibility across systems and programming languages
- **Unified API Format**: Each category follows a consistent pattern, reducing learning curve
- **Protocol Abstraction**: Low-level details are handled by the driver, simplifying application code
- **Reliable Communication**: Mandatory acknowledgment mechanism replaces timeout-based detection
- **Clear Addressing Mechanism**: Explicit documentation of how A-series and U-series commands use the slave address set by S001
- **Non-Blocking Design for Airborne Environments**:
  - Critical for systems that must process multiple tasks concurrently
  - Prevents slow serial communication from blocking application threads
  - Ensures responsiveness in time-critical airborne applications
  - Implements asynchronous operation with background thread processing
- **Enhanced Error Handling**:
  - Categorized error codes (transient vs. permanent) for intelligent recovery
  - Callback mechanism for asynchronous error notification
  - Example retry logic for transient errors
  - Consistent error handling across Windows and Linux platforms
- **Flexible Response Handling**:
  - Support for both callback-based (event-driven) and polling-based (sequential) approaches
  - Clear guidance on when to use each approach
  - Examples for all response handling patterns
- **Comprehensive Buffer Management**:
  - Fixed-size payload buffers (5×12 bytes uplink, 10×12 bytes downlink) focused on valid data
  - FIFO buffer system with data ready notification
  - Advanced overflow protection with configurable policies
  - Buffer threshold callbacks for proactive management
- **Runtime Safety Checks**: Detection of multiple devices during broadcasting operations
- **Cross-Platform Data Validation**: Helper functions ensure data integrity across different systems and languages
- **Performance Optimization**:
  - Non-blocking design keeps application threads responsive
  - FIFO buffer system prevents data loss during slow serial transmission
  - Optimized 12-byte payload management eliminates unnecessary overhead

### 9.3 Benefits

This design addresses the specific requirements outlined in the RS485 Communication Software Protocol document, particularly:
- The need for reliable broadcast communication with acknowledgment
- Non-blocking operation for airborne environments with multiple concurrent tasks
- Comprehensive buffer management (256KB/512KB) to handle the inherently slow nature of serial communication
- Clear documentation of the U-series command addressing mechanism
- Intelligent error handling with transient vs. permanent categorization and retry logic
- Flexible response handling approaches with clear guidance on when to use each
- Runtime safety checks to prevent common issues
- FIFO buffer system with data ready notification to prevent data loss

The result is a modular, extensible, and reliable communication system that can be easily integrated into various applications while maintaining performance in challenging airborne environments. The non-blocking design ensures that slow serial communication does not interfere with other critical operations, while the comprehensive buffer management prevents data loss during periods of high system load. The error handling and response flexibility make the API robust and user-friendly.

## 10. Critical Design Updates Summary

### 10.0 Document Consistency Corrections (Latest Update)

**Major Consistency Fixes Applied:**
1. **Type Safety Enhancement**: Replaced generic `RS485Error` with specific result types throughout the document:
   - `ConfigurationResult` for system and user configuration operations
   - `RequestResult` for data request operations
   - `ResponseResult` for response handling operations
   - `ConnectionResult` for port and device connection operations
   - `BufferResult` for buffer management operations

2. **API Function Signature Consistency**: Ensured all function signatures match their declared return types:
   - `configureSystemSettings()` returns `ConfigurationResult`
   - `configureUserSettings()` returns `ConfigurationResult`
   - `requestData()` returns `RequestResult`
   - `receiveSlaveResponse()` returns `ResponseResult`
   - `open()` returns `ConnectionResult`
   - `getBufferStatus()` returns `BufferResult`

3. **Error Handling Examples Updated**: All code examples now use the correct specific result types instead of generic `RS485Error`

4. **API Summary Table Corrections**: Updated function signatures and descriptions to match the actual implementation

5. **Buffer Management Clarification**: Consistently described the 12-byte payload-centric buffer design throughout the document

### 10.1 Buffer Architecture Corrections

**Updated Buffer Specifications:**
- **Uplink Buffer**: 5 payload slots × 12 bytes = 60 bytes total
- **Downlink Buffer**: 10 payload slots × 12 bytes = 120 bytes total
- **Core Focus**: Buffer management only focuses on **12-byte payload** data, which is the only valid information

**Key Design Philosophy: Buffer Only Manages Valid Data**

Core design principles of the buffer management system:
- **Store only payload data**: Buffer stores only the 12-byte valid payload data, not the frame's header, CRC, trailer, etc.
- **Separate frame assembly/parsing**: The 16-byte frame structure (0xAA + ID + 12-byte payload + CRC + 0x0D) is only assembled during transmission and parsed during reception
- **Focus on valid data**: Avoid storing redundant frame structure data, focus on valid information

**Buffer Capacity Calculation:**
- **Uplink Buffer**: 5 payload slots × 12 bytes = 60 bytes
- **Downlink Buffer**: 10 payload slots × 12 bytes = 120 bytes
- **Total Capacity**: 60 + 120 = 180 bytes (pure valid data)

**Protocol Core**: 12-byte payload contains all core communication data:
  - System configuration commands (S-series): Configure system parameters
  - User configuration commands (U-series): User setting parameters
  - Application data requests (A-series): Application data queries
  - AI model weight/bias data (W-series): AI model weight data

**Design Advantages:**
- Simplified buffer management logic, focused on valid data processing
- Reduced unnecessary memory copying

### 10.2 Buffer Flag Management Implementation

**Critical Buffer Overflow Prevention:**
- **Pre-transmission Check**: Driver checks uplink buffer flag before sending data to ensure space is available
- **Pre-storage Check**: Driver checks downlink buffer flag before storing received data to prevent overflow
- **FIFO Guarantee**: Strict First-In-First-Out ordering maintained for both PC User side and driver side
- **Overflow Handling**: Configurable policies when buffers reach capacity (discard oldest/newest or trigger error)

**Buffer Flag Structure:**
```cpp
struct BufferFlags {
    bool uplinkFull;        // Uplink buffer full flag (when 5 payload slots used)
    bool downlinkFull;      // Downlink buffer full flag (when 10 payload slots used)
    uint32_t uplinkUsed;    // Current uplink usage (0-5 payload slots)
    uint32_t downlinkUsed;  // Current downlink usage (0-10 payload slots)
};
```

### 10.3 DeviceIoControl() Implementation Strategy

**API Design Philosophy:**
- **Internal Implementation**: DeviceIoControl() is used **internally within the API functions**, not exposed directly to users
- **User-Friendly Interface**: Applications use high-level API functions (configureSystemSettings, requestData, etc.)
- **Industry Standard**: Follows standard serial port communication interface patterns
- **Abstraction Layer**: Users don't need to understand IOCTL codes or buffer management details

**Data Exchange Mechanism:**
1. **Standard Windows Interface**: Uses the industry-standard Windows driver communication method
2. **Asynchronous I/O Support**: Enables non-blocking operations with overlapped I/O
3. **Buffer Management**: Efficient data transfer through system-managed buffers
4. **Error Handling**: Comprehensive error reporting through Windows error codes
5. **FIFO Guarantee**: DeviceIoControl() calls are queued and processed in strict FIFO order

### 10.4 Payload-Centric Data Flow

**Complete Data Flow Architecture:**
1. **Transmission Path**: PC User → Buffer Flag Check → 12-byte Payload Queue → Frame Assembly → RS485 Bus
2. **Reception Path**: RS485 Bus → Frame Parsing → 12-byte Payload Extraction → Buffer Flag Check → Payload Queue → PC User
3. **Core Principle**: All meaningful data exchange occurs through the 12-byte payload, making it the heart of the protocol

**Frame Processing Pipeline:**
```
FTDI VCP Driver → Filter Driver → Frame Parser → Payload Extractor → Buffer Flag Check → Buffer Manager → User Application
     ↓              ↓              ↓              ↓                ↓                ↓              ↓
  Hardware IRQ → Work Item → DPC Context → 12-byte payload → Check downlink flag → Ring Buffer → IOCTL Completion
```

### 10.5 Enhanced Driver Architecture

# AI-SLDAP RS485 Driver API Design Document

**Buffer Flag Manager:**
```cpp
class BufferFlagManager {
public:
    // Pre-transmission buffer flag check
    bool CheckUplinkSpaceAvailable();

    // Pre-storage buffer flag check
    bool CheckDownlinkSpaceAvailable();

    // Update flags after buffer operations
    void UpdateBufferFlags(size_t uplinkUsed, size_t downlinkUsed);
};
```

### 10.6 Implementation Summary

These critical updates ensure that:
1. **Buffer management is accurate** with correct 12-byte payload focus
2. **Overflow prevention is robust** with comprehensive flag checking
3. **FIFO ordering is guaranteed** for both PC and driver sides
4. **DeviceIoControl() usage is properly abstracted** within the API
5. **The 12-byte payload remains central** to all protocol operations

The updated design maintains the original API simplicity while providing the robust buffer management and overflow prevention mechanisms essential for reliable RS485 communication in airborne environments.

## 11. Function Code to API Category Mapping Summary

### 11.1 Complete Function Code Correspondence

The RS485 driver implements a **direct one-to-one mapping** between ZES protocol function codes and API categories, ensuring that each API call is automatically routed to the correct protocol handling mechanism:

| Function Code | Binary | Description | API Category | Buffer Check | Example Usage |
|:-------------:|:------:|:------------|:-------------|:-------------|:-------------|
| **0b111** | Assign data | **Master Broadcasting API** (S-series) | ✓ Uplink | `configureSystemSettings("S001", 5)` |
| **0b111** | Assign data | **Master Assign Data API** (U/W-series) | ✓ Uplink | `configureUserSettings("U001", 250)` |
| **0b110** | Request data | **Master Request API** (A-series) | ✓ Uplink | `requestData("A001")` |
| **0b010** | Response to Assign | **Slave Response API** (Acknowledgments) | ✓ Downlink | `receiveSlaveResponse(5, responseData)` |
| **0b001** | Response to Request | **Slave Response API** (Data responses) | ✓ Downlink | `receiveSlaveResponse(5, responseData)` |
| **0b000** | Re-send request | **Error Handle API** (Retry mechanism) | N/A | Automatic retry handling |

### 11.2 FTDI-Style Management Integration

The driver provides comprehensive management functions following industry-standard patterns:

**Port Management (Similar to FTDI FT_Open, FT_Close, FT_ListDevices):**
- `openPort()`, `closePort()`, `isPortOpen()`, `enumerateDevices()`
- `detectMultipleDevices()`, `getPortInfo()`, `getBaudRate()`

**Buffer Management (Critical for RS485 Communication):**
- `getBufferStatus()`, `checkUplinkBufferFlag()`, `checkDownlinkBufferFlag()`
- `clearBuffer()`, `setBufferOverflowPolicy()`, `getBufferCapacity()`
- `setBufferThreshold()`, `registerBufferThresholdCallback()`

**Hardware Status (Similar to FTDI FT_GetStatus):**
- `getHardwareStatus()`, `getPerformanceMetrics()`, `getLineStatus()`

### 11.3 Automatic Buffer Flag Checking

**Every data transmission operation includes mandatory buffer verification:**

1. **Before Transmission**: Uplink buffer flag checked to ensure space availability
2. **Before Storage**: Downlink buffer flag checked to prevent overflow
3. **FIFO Guarantee**: Strict First-In-First-Out ordering maintained
4. **Overflow Policies**: Configurable handling (DISCARD_OLDEST, DISCARD_NEWEST, TRIGGER_ERROR)

**Implementation Example:**
```cpp
// Automatic buffer checking in every API call
ConfigurationResult configureSystemSettings(const std::string& commandKey, uint64_t value) {
    // Step 1: Mandatory buffer flag check
    if (!checkUplinkBufferFlag()) {
        return ConfigurationResult::BUFFER_OVERFLOW;
    }

    // Step 2: Function code validation (0b111 for S-series)
    if (!isValidSystemCommand(commandKey)) {
        return ConfigurationResult::INVALID_PARAMETER;
    }

    // Step 3: Process with automatic function code routing
    return processAssignDataCommand(0b111, 0x00, commandKey, value);
}
```

### 11.4 Enhanced Error Management

**Comprehensive error handling with FTDI integration:**

- **FTDI Driver Errors (100-199)**: Direct mapping from FTDI VCP driver errors
- **Buffer Management Errors (150-199)**: Critical for RS485 operation
- **Protocol Errors (200-299)**: ZES-specific error handling with automatic retry
- **Function Code Errors (500-599)**: Ensures API calls match protocol requirements

**Error categorization enables intelligent recovery:**
- **Transient errors**: May succeed on retry (TIMEOUT_ERROR, CRC_ERROR, DEVICE_BUSY)
- **Permanent errors**: Require user intervention (INVALID_PARAMETER, DEVICE_NOT_FOUND)

This comprehensive design ensures that the five API categories directly correspond to the ZES protocol function codes, providing a reliable and industry-standard interface for RS485 communication while maintaining automatic buffer management and error handling.
